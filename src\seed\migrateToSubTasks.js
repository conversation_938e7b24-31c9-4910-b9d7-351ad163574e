const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Task = require('../models/Task');
const SubTask = require('../models/SubTask');

dotenv.config();

const migrateTasksToSubTasks = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');
    console.log('Starting migration...');

    // Tìm tất cả tasks chưa có subtasks
    const tasksToMigrate = await Task.find({
      $or: [
        { subTasks: { $exists: false } },
        { subTasks: { $size: 0 } },
        { totalSubTasks: { $exists: false } }
      ]
    });

    console.log(`Found ${tasksToMigrate.length} tasks to migrate`);

    for (let task of tasksToMigrate) {
      console.log(`Migrating task: ${task.title}`);

      // Tạo subtask từ description hiện tại
      const subTask = new SubTask({
        title: task.description || task.title,
        description: '',
        isCompleted: task.isCompleted || false,
        order: 0,
        completedAt: task.isCompleted ? task.completedAt : null
      });

      await subTask.save();

      // Cập nhật task
      task.subTasks = [subTask._id];
      task.totalSubTasks = 1;
      task.completedSubTasks = task.isCompleted ? 1 : 0;

      await task.save();

      console.log(`✓ Migrated task: ${task.title}`);
    }

    console.log('Migration completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('Migration error:', error);
    process.exit(1);
  }
};

// Chạy migration
migrateTasksToSubTasks();
