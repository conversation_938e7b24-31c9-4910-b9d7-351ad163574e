const mongoose = require('mongoose');
const Task = require('../models/Task');
const SubTask = require('../models/SubTask');
const Category = require('../models/Category');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const seedTasks = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding tasks');

    // Lấy danh sách users và categories
    const users = await User.find({});
    const categories = await Category.find({});
    
    if (users.length === 0) {
      console.log('❌ No users found. Please run seed:users first');
      process.exit(1);
    }
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please run seed:categories first');
      process.exit(1);
    }

    // <PERSON><PERSON>a dữ liệu tasks và subtasks cũ
    await Task.deleteMany({});
    await SubTask.deleteMany({});
    console.log('Cleared existing tasks and subtasks');

    // Dữ liệu tasks mẫu theo category với subtasks
    const taskTemplatesByCategory = {
      'Công việc': [
        {
          title: 'Hoàn thành báo cáo tháng',
          description: 'Viết báo cáo tổng kết công việc tháng này và gửi cho quản lý',
          priority: 'high',
          daysFromNow: 7,
          subTasks: [
            { title: 'Thu thập dữ liệu từ các phòng ban', description: 'Liên hệ và thu thập báo cáo từ từng phòng ban' },
            { title: 'Phân tích và tổng hợp dữ liệu', description: 'Xử lý và phân tích các số liệu đã thu thập' },
            { title: 'Viết nội dung báo cáo', description: 'Soạn thảo nội dung báo cáo chi tiết' },
            { title: 'Review và hoàn thiện', description: 'Kiểm tra lại và chỉnh sửa báo cáo' },
            { title: 'Gửi báo cáo cho quản lý', description: 'Email báo cáo cuối cùng cho ban lãnh đạo' }
          ]
        },
        {
          title: 'Chuẩn bị presentation',
          description: 'Làm slide presentation cho cuộc họp tuần tới',
          priority: 'medium',
          daysFromNow: 5,
          subTasks: [
            { title: 'Lên outline cho presentation', description: 'Xác định cấu trúc và nội dung chính' },
            { title: 'Tạo slides PowerPoint', description: 'Thiết kế và làm slides đẹp mắt' },
            { title: 'Chuẩn bị demo', description: 'Chuẩn bị phần demo sản phẩm nếu có' },
            { title: 'Luyện tập thuyết trình', description: 'Tập nói và kiểm tra thời gian' }
          ]
        },
        {
          title: 'Review code của team',
          description: 'Kiểm tra và review code của các thành viên trong team',
          priority: 'medium',
          daysFromNow: 3,
          subTasks: [
            { title: 'Review pull request #123', description: 'Kiểm tra code của Minh' },
            { title: 'Review pull request #124', description: 'Kiểm tra code của Hoa' },
            { title: 'Feedback và merge', description: 'Đưa ra feedback và merge code' }
          ]
        }
      ],
      'Học tập': [
        {
          title: 'Học Node.js',
          description: 'Hoàn thành khóa học Node.js trên Udemy',
          priority: 'medium',
          daysFromNow: 14,
          subTasks: [
            { title: 'Xem video về Express.js', description: 'Học cách tạo server với Express' },
            { title: 'Làm bài tập về MongoDB', description: 'Thực hành kết nối và query database' },
            { title: 'Xây dựng API RESTful', description: 'Tạo một API hoàn chỉnh' },
            { title: 'Deploy lên Heroku', description: 'Triển khai ứng dụng lên cloud' }
          ]
        },
        {
          title: 'Đọc sách "Clean Code"',
          description: 'Đọc và ghi chú những điểm quan trọng trong sách Clean Code',
          priority: 'low',
          daysFromNow: 21,
          subTasks: [
            { title: 'Đọc chương 1-3', description: 'Meaningful Names, Functions, Comments' },
            { title: 'Đọc chương 4-6', description: 'Formatting, Objects and Data Structures' },
            { title: 'Đọc chương 7-9', description: 'Error Handling, Boundaries, Unit Tests' },
            { title: 'Tổng kết và áp dụng', description: 'Viết summary và áp dụng vào dự án' }
          ]
        },
        {
          title: 'Học tiếng Anh',
          description: 'Học 30 từ vựng mới và luyện speaking',
          priority: 'medium',
          daysFromNow: 1,
          subTasks: [
            { title: 'Học 10 từ vựng về công nghệ', description: 'Thuộc lòng và làm ví dụ' },
            { title: 'Học 10 từ vựng về business', description: 'Áp dụng vào câu giao tiếp' },
            { title: 'Học 10 từ vựng về đời sống', description: 'Sử dụng trong hội thoại hàng ngày' },
            { title: 'Luyện speaking 30 phút', description: 'Tập nói với app hoặc bạn bè' }
          ]
        }
      ],
      'Sức khỏe': [
        {
          title: 'Tập thể dục',
          description: 'Tập gym 3 lần trong tuần',
          priority: 'high',
          daysFromNow: 3
        },
        {
          title: 'Khám sức khỏe định kỳ',
          description: 'Đặt lịch và đi khám sức khỏe tổng quát',
          priority: 'medium',
          daysFromNow: 30
        }
      ],
      'Gia đình': [
        {
          title: 'Mua quà sinh nhật mẹ',
          description: 'Chọn và mua quà sinh nhật cho mẹ',
          priority: 'high',
          daysFromNow: 10
        },
        {
          title: 'Gọi điện cho bà ngoại',
          description: 'Gọi điện thăm hỏi sức khỏe bà ngoại',
          priority: 'medium',
          daysFromNow: 2
        }
      ],
            'Cá nhân': [
        {
          title: 'Dọn dẹp phòng ngủ',
          description: 'Sắp xếp lại đồ đạc và dọn dẹp phòng ngủ',
          priority: 'low',
          daysFromNow: 2
        },
        {
          title: 'Cập nhật CV',
          description: 'Cập nhật thông tin và kinh nghiệm mới vào CV',
          priority: 'medium',
          daysFromNow: 15
        }
      ],
      'Mua sắm': [
        {
          title: 'Mua thực phẩm tuần này',
          description: 'Đi siêu thị mua thực phẩm cho cả tuần',
          priority: 'high',
          daysFromNow: 1
        },
        {
          title: 'Mua quần áo mùa đông',
          description: 'Mua áo khoác và quần áo ấm cho mùa đông',
          priority: 'medium',
          daysFromNow: 20
        }
      ],
      'Giải trí': [
        {
          title: 'Xem phim mới',
          description: 'Xem bộ phim mới được đề xuất trên Netflix',
          priority: 'low',
          daysFromNow: 3
        },
        {
          title: 'Chơi game với bạn bè',
          description: 'Tổ chức buổi chơi game online với nhóm bạn',
          priority: 'low',
          daysFromNow: 5
        }
      ],
      'Du lịch': [
        {
          title: 'Lên kế hoạch du lịch Đà Lạt',
          description: 'Tìm hiểu địa điểm, đặt khách sạn và lập lịch trình',
          priority: 'medium',
          daysFromNow: 45
        },
        {
          title: 'Đặt vé máy bay',
          description: 'Đặt vé máy bay cho chuyến du lịch cuối năm',
          priority: 'high',
          daysFromNow: 60
        }
      ]
    };

    // Tạo tasks với subtasks cho mỗi user
    let totalTasksCreated = 0;
    let totalSubTasksCreated = 0;

    for (const user of users) {
      const userCategories = categories.filter(cat => cat.userId.toString() === user._id.toString());

      for (const category of userCategories) {
        const categoryTasks = taskTemplatesByCategory[category.name] || [];

        // Tạo 2-3 tasks cho mỗi category
        const numTasks = Math.min(categoryTasks.length, Math.floor(Math.random() * 2) + 2);

        for (let i = 0; i < numTasks; i++) {
          const template = categoryTasks[i];
          if (!template) continue;

          const startDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
          const deadline = new Date(Date.now() + template.daysFromNow * 24 * 60 * 60 * 1000);

          // Tạo task trước
          const task = new Task({
            title: `${template.title} - ${user.username}`,
            description: template.description,
            categoryId: category._id,
            startDate: startDate,
            deadline: deadline,
            priority: template.priority,
            userId: user._id,
            createdAt: startDate,
            subTasks: [],
            totalSubTasks: 0,
            completedSubTasks: 0,
            isCompleted: false
          });

          await task.save();
          totalTasksCreated++;

          // Tạo subtasks cho task này
          const createdSubTaskIds = [];
          let completedSubTasksCount = 0;

          for (let j = 0; j < template.subTasks.length; j++) {
            const subTaskTemplate = template.subTasks[j];

            // Random 40% subtasks đã hoàn thành
            const isSubTaskCompleted = Math.random() < 0.4;
            if (isSubTaskCompleted) completedSubTasksCount++;

            const subTask = new SubTask({
              title: subTaskTemplate.title,
              description: subTaskTemplate.description,
              order: j,
              isCompleted: isSubTaskCompleted,
              completedAt: isSubTaskCompleted ? new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime())) : undefined,
              createdAt: startDate
            });

            await subTask.save();
            createdSubTaskIds.push(subTask._id);
            totalSubTasksCreated++;
          }

          // Cập nhật task với subtasks
          task.subTasks = createdSubTaskIds;
          task.totalSubTasks = createdSubTaskIds.length;
          task.completedSubTasks = completedSubTasksCount;
          task.isCompleted = (completedSubTasksCount === createdSubTaskIds.length);

          if (task.isCompleted) {
            task.completedAt = new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime()));
          }

          await task.save();
        }
      }
    }

    console.log(`✅ Created ${totalTasksCreated} tasks with ${totalSubTasksCreated} subtasks successfully`);

    // In thống kê
    console.log('\n📊 Task Statistics:');
    const allTasks = await Task.find({}).populate('categoryId', 'name').populate('subTasks');

    for (const user of users) {
      const userTasks = allTasks.filter(task => task.userId.toString() === user._id.toString());
      const completedTasks = userTasks.filter(task => task.isCompleted);
      const pendingTasks = userTasks.filter(task => !task.isCompleted);

      console.log(`👤 ${user.username}: ${userTasks.length} tasks (${completedTasks.length} completed, ${pendingTasks.length} pending)`);
    }

    console.log('\n🎯 Sample tasks with subtasks:');
    const sampleTasks = allTasks.slice(0, 2);
    sampleTasks.forEach(task => {
      console.log(`- ${task.title} (${task.priority} priority)`);
      console.log(`  └─ ${task.totalSubTasks} subtasks (${task.completedSubTasks} completed, ${task.completionPercentage}%)`);
      task.subTasks.slice(0, 2).forEach(subTask => {
        console.log(`     ${subTask.isCompleted ? '✅' : '⏳'} ${subTask.title}`);
      });
    });

    mongoose.connection.close();
    console.log('\nDatabase connection closed');
  } catch (error) {
    console.error('Error seeding tasks:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedTasks();
}

module.exports = seedTasks;
          