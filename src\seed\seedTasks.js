const mongoose = require('mongoose');
const Task = require('../models/Task');
const SubTask = require('../models/SubTask');
const Category = require('../models/Category');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const seedTasks = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding tasks');

    // Lấy danh sách users và categories
    const users = await User.find({});
    const categories = await Category.find({});
    
    if (users.length === 0) {
      console.log('❌ No users found. Please run seed:users first');
      process.exit(1);
    }
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please run seed:categories first');
      process.exit(1);
    }

    // <PERSON><PERSON>a dữ liệu tasks và subtasks cũ
    await Task.deleteMany({});
    await SubTask.deleteMany({});
    console.log('Cleared existing tasks and subtasks');

    // Dữ liệu tasks mẫu theo category với subtasks
    const taskTemplatesByCategory = {
      'Công việc': [
        {
          title: 'Hoàn thành báo cáo tháng',
          description: 'Viết báo cáo tổng kết công việc tháng này và gửi cho quản lý',
          priority: 'high',
          daysFromNow: 7,
          subTasks: [
            { title: 'Thu thập dữ liệu từ các phòng ban', description: 'Liên hệ và thu thập báo cáo từ từng phòng ban' },
            { title: 'Phân tích và tổng hợp dữ liệu', description: 'Xử lý và phân tích các số liệu đã thu thập' },
            { title: 'Viết nội dung báo cáo', description: 'Soạn thảo nội dung báo cáo chi tiết' },
            { title: 'Review và hoàn thiện', description: 'Kiểm tra lại và chỉnh sửa báo cáo' },
            { title: 'Gửi báo cáo cho quản lý', description: 'Email báo cáo cuối cùng cho ban lãnh đạo' }
          ]
        },
        {
          title: 'Chuẩn bị presentation',
          description: 'Làm slide presentation cho cuộc họp tuần tới',
          priority: 'medium',
          daysFromNow: 5,
          subTasks: [
            { title: 'Lên outline cho presentation', description: 'Xác định cấu trúc và nội dung chính' },
            { title: 'Tạo slides PowerPoint', description: 'Thiết kế và làm slides đẹp mắt' },
            { title: 'Chuẩn bị demo', description: 'Chuẩn bị phần demo sản phẩm nếu có' },
            { title: 'Luyện tập thuyết trình', description: 'Tập nói và kiểm tra thời gian' }
          ]
        },
        {
          title: 'Review code của team',
          description: 'Kiểm tra và review code của các thành viên trong team',
          priority: 'medium',
          daysFromNow: 3,
          subTasks: [
            { title: 'Review pull request #123', description: 'Kiểm tra code của Minh' },
            { title: 'Review pull request #124', description: 'Kiểm tra code của Hoa' },
            { title: 'Feedback và merge', description: 'Đưa ra feedback và merge code' }
          ]
        }
      ],
      'Học tập': [
        {
          title: 'Học Node.js',
          description: 'Hoàn thành khóa học Node.js trên Udemy',
          priority: 'medium',
          daysFromNow: 14,
          subTasks: [
            { title: 'Xem video về Express.js', description: 'Học cách tạo server với Express' },
            { title: 'Làm bài tập về MongoDB', description: 'Thực hành kết nối và query database' },
            { title: 'Xây dựng API RESTful', description: 'Tạo một API hoàn chỉnh' },
            { title: 'Deploy lên Heroku', description: 'Triển khai ứng dụng lên cloud' }
          ]
        },
        {
          title: 'Đọc sách "Clean Code"',
          description: 'Đọc và ghi chú những điểm quan trọng trong sách Clean Code',
          priority: 'low',
          daysFromNow: 21,
          subTasks: [
            { title: 'Đọc chương 1-3', description: 'Meaningful Names, Functions, Comments' },
            { title: 'Đọc chương 4-6', description: 'Formatting, Objects and Data Structures' },
            { title: 'Đọc chương 7-9', description: 'Error Handling, Boundaries, Unit Tests' },
            { title: 'Tổng kết và áp dụng', description: 'Viết summary và áp dụng vào dự án' }
          ]
        },
        {
          title: 'Học tiếng Anh',
          description: 'Học 30 từ vựng mới và luyện speaking',
          priority: 'medium',
          daysFromNow: 1,
          subTasks: [
            { title: 'Học 10 từ vựng về công nghệ', description: 'Thuộc lòng và làm ví dụ' },
            { title: 'Học 10 từ vựng về business', description: 'Áp dụng vào câu giao tiếp' },
            { title: 'Học 10 từ vựng về đời sống', description: 'Sử dụng trong hội thoại hàng ngày' },
            { title: 'Luyện speaking 30 phút', description: 'Tập nói với app hoặc bạn bè' }
          ]
        }
      ],
      'Sức khỏe': [
        {
          title: 'Tập thể dục',
          description: 'Tập gym 3 lần trong tuần',
          priority: 'medium',
          daysFromNow: 7,
          subTasks: [
            { title: 'Tập ngực và vai', description: 'Bench press, shoulder press' },
            { title: 'Tập lưng và tay', description: 'Pull-up, rowing, bicep curl' },
            { title: 'Tập chân và mông', description: 'Squat, deadlift, lunges' }
          ]
        },
        {
          title: 'Khám sức khỏe định kỳ',
          description: 'Đi khám sức khỏe tổng quát',
          priority: 'high',
          daysFromNow: 30,
          subTasks: [
            { title: 'Đặt lịch hẹn bác sĩ', description: 'Gọi điện đặt lịch khám' },
            { title: 'Chuẩn bị giấy tờ', description: 'BHYT, CMND, sổ khám bệnh' },
            { title: 'Đi khám và lấy kết quả', description: 'Thực hiện khám và nhận kết quả' }
          ]
        }
      ],
      'Phát triển bản thân': [
        {
          title: 'Đọc sách phát triển bản thân',
          description: 'Đọc 2 cuốn sách về kỹ năng mềm',
          priority: 'medium',
          daysFromNow: 30,
          subTasks: [
            { title: 'Đọc "7 Habits of Highly Effective People"', description: 'Hoàn thành cuốn sách đầu tiên' },
            { title: 'Đọc "How to Win Friends and Influence People"', description: 'Hoàn thành cuốn sách thứ hai' },
            { title: 'Viết tóm tắt và áp dụng', description: 'Ghi chú những điểm quan trọng' }
          ]
        },
        {
          title: 'Học kỹ năng mới',
          description: 'Học một kỹ năng mới trong tháng này',
          priority: 'low',
          daysFromNow: 25,
          subTasks: [
            { title: 'Chọn kỹ năng muốn học', description: 'Quyết định học gì: design, marketing, etc' },
            { title: 'Tìm khóa học online', description: 'Tìm khóa học phù hợp trên Udemy/Coursera' },
            { title: 'Hoàn thành 50% khóa học', description: 'Học ít nhất một nửa khóa học' }
          ]
        },
        {
          title: 'Cập nhật CV',
          description: 'Cập nhật thông tin và kinh nghiệm mới vào CV',
          priority: 'medium',
          daysFromNow: 15,
          subTasks: [
            { title: 'Cập nhật thông tin cá nhân', description: 'Địa chỉ, số điện thoại, email mới' },
            { title: 'Thêm kinh nghiệm làm việc mới', description: 'Mô tả công việc và thành tích gần đây' },
            { title: 'Cải thiện design CV', description: 'Làm CV đẹp hơn và chuyên nghiệp' }
          ]
        }
      ],
      'Mua sắm': [
        {
          title: 'Mua thực phẩm tuần này',
          description: 'Đi siêu thị mua thực phẩm cho cả tuần',
          priority: 'high',
          daysFromNow: 1,
          subTasks: [
            { title: 'Lập danh sách mua sắm', description: 'Viết list thực phẩm cần mua' },
            { title: 'Đi siêu thị', description: 'Mua theo danh sách đã lập' },
            { title: 'Sắp xếp tủ lạnh', description: 'Cất thực phẩm vào tủ lạnh ngăn nắp' }
          ]
        },
        {
          title: 'Mua quần áo mùa đông',
          description: 'Mua áo khoác và quần áo ấm cho mùa đông',
          priority: 'medium',
          daysFromNow: 20,
          subTasks: [
            { title: 'Kiểm tra tủ đồ hiện tại', description: 'Xem còn thiếu gì cho mùa đông' },
            { title: 'Tìm hiểu xu hướng thời trang', description: 'Xem các shop online và offline' },
            { title: 'Mua sắm và thử đồ', description: 'Đi mua và thử cho vừa' }
          ]
        }
      ],
      'Giải trí': [
        {
          title: 'Xem phim mới',
          description: 'Xem bộ phim mới được đề xuất trên Netflix',
          priority: 'low',
          daysFromNow: 3,
          subTasks: [
            { title: 'Tìm phim hay trên Netflix', description: 'Browse và đọc review phim' },
            { title: 'Chuẩn bị đồ ăn vặt', description: 'Mua popcorn, nước ngọt' },
            { title: 'Xem phim và thư giãn', description: 'Enjoy movie night' }
          ]
        },
        {
          title: 'Chơi game với bạn bè',
          description: 'Tổ chức buổi chơi game online với nhóm bạn',
          priority: 'low',
          daysFromNow: 5,
          subTasks: [
            { title: 'Hẹn lịch với bạn bè', description: 'Tạo group chat và hẹn giờ' },
            { title: 'Chuẩn bị game', description: 'Download và update game' },
            { title: 'Chơi game cùng nhau', description: 'Have fun gaming session' }
          ]
        }
      ],
      'Du lịch': [
        {
          title: 'Lên kế hoạch du lịch Đà Lạt',
          description: 'Tìm hiểu địa điểm, đặt khách sạn và lập lịch trình',
          priority: 'medium',
          daysFromNow: 45,
          subTasks: [
            { title: 'Research địa điểm du lịch', description: 'Tìm hiểu các điểm tham quan ở Đà Lạt' },
            { title: 'Đặt khách sạn', description: 'Book hotel hoặc homestay' },
            { title: 'Lập lịch trình chi tiết', description: 'Plan từng ngày sẽ đi đâu, làm gì' },
            { title: 'Chuẩn bị đồ đạc', description: 'Pack quần áo và đồ cần thiết' }
          ]
        },
        {
          title: 'Đặt vé máy bay',
          description: 'Đặt vé máy bay cho chuyến du lịch cuối năm',
          priority: 'high',
          daysFromNow: 60,
          subTasks: [
            { title: 'So sánh giá vé', description: 'Check giá trên các website' },
            { title: 'Chọn chuyến bay phù hợp', description: 'Chọn giờ bay và hãng hàng không' },
            { title: 'Thanh toán và nhận vé', description: 'Complete booking và lưu e-ticket' }
          ]
        }
      ]
    };

    // Tạo tasks với subtasks cho mỗi user
    let totalTasksCreated = 0;
    let totalSubTasksCreated = 0;

    for (const user of users) {
      const userCategories = categories.filter(cat => cat.userId.toString() === user._id.toString());

      for (const category of userCategories) {
        const categoryTasks = taskTemplatesByCategory[category.name] || [];

        // Tạo 2-3 tasks cho mỗi category
        const numTasks = Math.min(categoryTasks.length, Math.floor(Math.random() * 2) + 2);

        for (let i = 0; i < numTasks; i++) {
          const template = categoryTasks[i];
          if (!template) continue;

          const startDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
          const deadline = new Date(Date.now() + template.daysFromNow * 24 * 60 * 60 * 1000);

          // Tạo task trước
          const task = new Task({
            title: `${template.title} - ${user.username}`,
            description: template.description,
            categoryId: category._id,
            startDate: startDate,
            deadline: deadline,
            priority: template.priority,
            userId: user._id,
            createdAt: startDate,
            subTasks: [],
            totalSubTasks: 0,
            completedSubTasks: 0,
            isCompleted: false
          });

          await task.save();
          totalTasksCreated++;

          // Tạo subtasks cho task này
          const createdSubTaskIds = [];
          let completedSubTasksCount = 0;

          for (let j = 0; j < template.subTasks.length; j++) {
            const subTaskTemplate = template.subTasks[j];

            // Random 40% subtasks đã hoàn thành
            const isSubTaskCompleted = Math.random() < 0.4;
            if (isSubTaskCompleted) completedSubTasksCount++;

            const subTask = new SubTask({
              title: subTaskTemplate.title,
              description: subTaskTemplate.description,
              order: j,
              isCompleted: isSubTaskCompleted,
              completedAt: isSubTaskCompleted ? new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime())) : undefined,
              createdAt: startDate
            });

            await subTask.save();
            createdSubTaskIds.push(subTask._id);
            totalSubTasksCreated++;
          }

          // Cập nhật task với subtasks
          task.subTasks = createdSubTaskIds;
          task.totalSubTasks = createdSubTaskIds.length;
          task.completedSubTasks = completedSubTasksCount;
          task.isCompleted = (completedSubTasksCount === createdSubTaskIds.length);

          if (task.isCompleted) {
            task.completedAt = new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime()));
          }

          await task.save();
        }
      }
    }

    console.log(`✅ Created ${totalTasksCreated} tasks with ${totalSubTasksCreated} subtasks successfully`);

    // In thống kê
    console.log('\n📊 Task Statistics:');
    const allTasks = await Task.find({}).populate('categoryId', 'name').populate('subTasks');

    for (const user of users) {
      const userTasks = allTasks.filter(task => task.userId.toString() === user._id.toString());
      const completedTasks = userTasks.filter(task => task.isCompleted);
      const pendingTasks = userTasks.filter(task => !task.isCompleted);

      console.log(`👤 ${user.username}: ${userTasks.length} tasks (${completedTasks.length} completed, ${pendingTasks.length} pending)`);
    }

    console.log('\n🎯 Sample tasks with subtasks:');
    const sampleTasks = allTasks.slice(0, 2);
    sampleTasks.forEach(task => {
      console.log(`- ${task.title} (${task.priority} priority)`);
      console.log(`  └─ ${task.totalSubTasks} subtasks (${task.completedSubTasks} completed, ${task.completionPercentage}%)`);
      task.subTasks.slice(0, 2).forEach(subTask => {
        console.log(`     ${subTask.isCompleted ? '✅' : '⏳'} ${subTask.title}`);
      });
    });

    mongoose.connection.close();
    console.log('\nDatabase connection closed');
  } catch (error) {
    console.error('Error seeding tasks:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedTasks();
}

module.exports = seedTasks;
          