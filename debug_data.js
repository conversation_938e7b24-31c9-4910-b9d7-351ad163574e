// Debug script để kiểm tra data trong database
const mongoose = require('mongoose');
const Task = require('./src/models/Task');
const SubTask = require('./src/models/SubTask');

const USER_ID = '68635f9b6f882821ae2a315c';

async function debugData() {
  try {
    // Connect to MongoDB (same as .env)
    await mongoose.connect('mongodb://localhost:27017/todo-list');
    console.log('Connected to MongoDB');

    console.log('\n=== Debug Database Data ===');

    // 0. Check all users and tasks in database
    const allTasks = await Task.find({}).limit(10);
    console.log(`\nTotal tasks in database: ${allTasks.length}`);

    if (allTasks.length > 0) {
      console.log('\nAll userIds in database:');
      const userIds = [...new Set(allTasks.map(t => t.userId.toString()))];
      userIds.forEach((uid, index) => {
        console.log(`  ${index + 1}. ${uid}`);
      });

      console.log('\nFirst few tasks:');
      allTasks.slice(0, 3).forEach((task, index) => {
        console.log(`  ${index + 1}. ${task.title} (User: ${task.userId})`);
      });
    }

    // 1. Get all tasks for user
    const tasks = await Task.find({ userId: USER_ID })
      .populate('subTasks')
      .sort({ createdAt: -1 });

    console.log(`\nFound ${tasks.length} tasks for user ${USER_ID}`);
    
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      console.log(`\nTask ${i + 1}: ${task.title}`);
      console.log(`  - Task ID: ${task._id}`);
      console.log(`  - Completed: ${task.isCompleted}`);
      console.log(`  - Total SubTasks: ${task.totalSubTasks}`);
      console.log(`  - Completed SubTasks: ${task.completedSubTasks}`);
      console.log(`  - SubTasks array length: ${task.subTasks.length}`);
      
      if (task.subTasks.length > 0) {
        console.log(`  - SubTasks:`);
        task.subTasks.forEach((st, stIndex) => {
          console.log(`    ${stIndex + 1}. ${st.title}`);
          console.log(`       ID: ${st._id}`);
          console.log(`       Completed: ${st.isCompleted}`);
          console.log(`       Order: ${st.order}`);
        });
      } else {
        console.log(`  - No SubTasks found`);
      }
      
      // Check if SubTask IDs in task.subTasks array match actual SubTask documents
      if (task.subTasks.length > 0) {
        console.log(`  - Checking SubTask ID consistency...`);
        for (const subTask of task.subTasks) {
          const subTaskDoc = await SubTask.findById(subTask._id);
          if (!subTaskDoc) {
            console.log(`    ❌ SubTask ${subTask._id} NOT FOUND in SubTask collection!`);
          } else {
            console.log(`    ✅ SubTask ${subTask._id} exists in SubTask collection`);
          }
        }
      }
    }
    
    // 2. Check for orphaned SubTasks
    console.log(`\n=== Checking for orphaned SubTasks ===`);
    const allSubTasks = await SubTask.find({});
    console.log(`Total SubTasks in database: ${allSubTasks.length}`);
    
    for (const subTask of allSubTasks) {
      const parentTask = await Task.findOne({ subTasks: subTask._id });
      if (!parentTask) {
        console.log(`❌ Orphaned SubTask found: ${subTask.title} (ID: ${subTask._id})`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run debug
debugData();
