<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="8dp"
    android:background="?attr/selectableItemBackground">

    <!-- Checkbox for subtask completion -->
    <CheckBox
        android:id="@+id/checkboxSubTask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:scaleX="0.8"
        android:scaleY="0.8" />

    <!-- Subtask content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textSubTaskTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="SubTask title"
            android:textSize="14sp"
            android:textColor="@android:color/black"
            android:maxLines="2"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/textSubTaskDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="SubTask description"
            android:textSize="12sp"
            android:textColor="#888888"
            android:maxLines="1"
            android:ellipsize="end"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
