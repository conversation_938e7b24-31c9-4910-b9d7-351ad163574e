const mongoose = require('mongoose');
const User = require('../models/User');
const Category = require('../models/Category');
const Task = require('../models/Task');
const SubTask = require('../models/SubTask');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

async function getTestData() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');
    
    // Lấy user admin
    const adminUser = await User.findOne({ username: 'admin' });
    console.log('🔑 USER ID (admin):', adminUser._id.toString());
    
    // Lấy categories của admin
    const categories = await Category.find({ userId: adminUser._id }).limit(3);
    console.log('\n📂 CATEGORY IDs (admin):');
    categories.forEach(cat => {
      console.log(`- ${cat.name}: ${cat._id.toString()}`);
    });
    
    // Lấy tasks của admin với subtasks
    const tasks = await Task.find({ userId: adminUser._id })
      .populate('categoryId', 'name')
      .populate('subTasks')
      .limit(2);
    
    console.log('\n📝 TASK IDs với SUBTASK IDs:');
    tasks.forEach(task => {
      console.log(`\n📋 Task: ${task.title}`);
      console.log(`   Task ID: ${task._id.toString()}`);
      console.log(`   Category: ${task.categoryId.name}`);
      console.log(`   SubTasks (${task.subTasks.length}):`);
      task.subTasks.forEach((subTask, index) => {
        console.log(`   ${index + 1}. ${subTask.title}`);
        console.log(`      SubTask ID: ${subTask._id.toString()}`);
        console.log(`      Completed: ${subTask.isCompleted}`);
      });
    });
    
    console.log('\n🎯 SAMPLE API TEST DATA:');
    console.log('='.repeat(50));
    console.log(`USER_ID: ${adminUser._id.toString()}`);
    console.log(`CATEGORY_ID: ${categories[0]._id.toString()}`);
    console.log(`TASK_ID: ${tasks[0]._id.toString()}`);
    console.log(`SUBTASK_ID: ${tasks[0].subTasks[0]._id.toString()}`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

getTestData();
