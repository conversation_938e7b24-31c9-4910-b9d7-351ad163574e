package com.example.myapp.activities;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;
import com.example.myapp.R;
import com.example.myapp.models.Task;
import com.example.myapp.models.Category;
import com.example.myapp.services.CategoryAPI;
import com.example.myapp.services.TaskAPI;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class AddTaskActivity extends AppCompatActivity {

    // Constants for result codes
    public static final int RESULT_TASK_CREATED = 1001;
    public static final String EXTRA_TASK_CREATED = "task_created";

    // Views
    private TextInputEditText editTaskTitle, editTaskDescription;
    private Spinner spinnerCategory;
    private RadioGroup radioGroupPriority;
    private TextView textStartDate, textDeadline;
    private Button buttonSelectStartDate, buttonSelectDeadline, buttonSave, buttonSelectImage, buttonRemoveImage;
    private ImageButton buttonBack;
    private ImageView imagePreview;

    // Data
    private Date selectedStartDate;
    private Date selectedDeadline;
    private String selectedImagePath;
    private List<Category> categories;
    private SimpleDateFormat dateFormat;

    // Image picker
    private ActivityResultLauncher<Intent> imagePickerLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_task);

        initViews();
        setupImagePicker();
        setupCategories();
        setupClickListeners();
        
        dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    }

    private void initViews() {
        editTaskTitle = findViewById(R.id.editTextTitle);
        editTaskDescription = findViewById(R.id.editTextDescription);
        spinnerCategory = findViewById(R.id.spinnerCategory);
        radioGroupPriority = findViewById(R.id.radioGroupPriority);
        textStartDate = findViewById(R.id.textStartDate);
        textDeadline = findViewById(R.id.textDeadline);
        buttonSelectStartDate = findViewById(R.id.buttonSelectStartDate);
        buttonSelectDeadline = findViewById(R.id.buttonSelectDeadline);
        buttonSave = findViewById(R.id.buttonSave);
        buttonBack = findViewById(R.id.buttonBack);
        buttonSelectImage = findViewById(R.id.buttonSelectImage);
        buttonRemoveImage = findViewById(R.id.buttonRemoveImage);
        imagePreview = findViewById(R.id.imagePreview);
    }

    private void setupImagePicker() {
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        Uri imageUri = result.getData().getData();
                        if (imageUri != null) {
                            selectedImagePath = imageUri.toString();
                            imagePreview.setImageURI(imageUri);
                            imagePreview.setVisibility(View.VISIBLE);
                            buttonRemoveImage.setVisibility(View.VISIBLE);
                        }
                    }
                }
        );
    }

    private void setupCategories() {
        categories = new ArrayList<>();

        // Gọi API để lấy categories thật
        loadCategoriesFromAPI();
    }

    /**
     * Gọi API để lấy danh sách categories
     */
    private void loadCategoriesFromAPI() {
        String userId = "685ba33d4ed50aba7252bb4d"; // Thay bằng userId thật

        Log.d("AddTaskActivity", "Bắt đầu load categories từ API...");

        CategoryAPI.getCategories(userId, new CategoryAPI.CategoryCallback() {
            @Override
            public void onSuccess(List<Category> categoriesFromAPI) {
                Log.d("AddTaskActivity", "API thành công! Nhận được " + categoriesFromAPI.size() + " categories");

                runOnUiThread(() -> {
                    // Cập nhật danh sách categories
                    categories.clear();
                    categories.addAll(categoriesFromAPI);

                    // Cập nhật Spinner
                    updateCategorySpinner();

                    Toast.makeText(AddTaskActivity.this,
                        "Đã tải " + categoriesFromAPI.size() + " chủ đề từ server",
                        Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("AddTaskActivity", "API lỗi: " + errorMessage);

                runOnUiThread(() -> {
                    Toast.makeText(AddTaskActivity.this,
                        "Lỗi tải chủ đề: " + errorMessage,
                        Toast.LENGTH_LONG).show();

                    // Fallback về dữ liệu mẫu nếu API lỗi
                    loadSampleCategories();
                });
            }
        });
    }

    /**
     * Cập nhật Spinner với danh sách categories mới
     */
    private void updateCategorySpinner() {
        List<String> categoryNames = new ArrayList<>();
        for (Category category : categories) {
            categoryNames.add(category.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categoryNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(adapter);
    }

    /**
     * Load dữ liệu mẫu nếu API lỗi
     */
    private void loadSampleCategories() {
        categories.clear();

        Category workCategory = new Category("Công việc", "Các nhiệm vụ liên quan đến công việc", "#2196F3", "work");
        workCategory.setId("1");
        categories.add(workCategory);

        Category personalCategory = new Category("Cá nhân", "Các nhiệm vụ cá nhân", "#4CAF50", "personal");
        personalCategory.setId("2");
        categories.add(personalCategory);

        Category studyCategory = new Category("Học tập", "Các nhiệm vụ học tập", "#FF9800", "study");
        studyCategory.setId("3");
        categories.add(studyCategory);

        updateCategorySpinner();
    }

    private void setupClickListeners() {
        buttonBack.setOnClickListener(v -> finish());

        buttonSelectStartDate.setOnClickListener(v -> showStartDatePicker());

        buttonSelectDeadline.setOnClickListener(v -> showDeadlinePicker());

        buttonSelectImage.setOnClickListener(v -> openImagePicker());

        buttonRemoveImage.setOnClickListener(v -> removeImage());

        buttonSave.setOnClickListener(v -> saveTask());
    }

    private void showStartDatePicker() {
        Calendar calendar = Calendar.getInstance();

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    selectedStartDate = calendar.getTime();
                    textStartDate.setText(dateFormat.format(selectedStartDate));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        datePickerDialog.show();
    }

    private void showDeadlinePicker() {
        Calendar calendar = Calendar.getInstance();

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    selectedDeadline = calendar.getTime();
                    textDeadline.setText(dateFormat.format(selectedDeadline));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        datePickerDialog.show();
    }

    private void openImagePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        imagePickerLauncher.launch(intent);
    }

    private void removeImage() {
        selectedImagePath = null;
        imagePreview.setImageResource(android.R.drawable.ic_menu_camera);
        buttonRemoveImage.setVisibility(View.GONE);
        Toast.makeText(this, "Đã xóa hình ảnh", Toast.LENGTH_SHORT).show();
    }

    private void saveTask() {
        String title = editTaskTitle.getText().toString().trim();
        String description = editTaskDescription.getText().toString().trim();

        // Validate input
        if (TextUtils.isEmpty(title)) {
            editTaskTitle.setError("Vui lòng nhập tiêu đề");
            editTaskTitle.requestFocus();
            return;
        }

        // Get selected category
        int selectedCategoryPosition = spinnerCategory.getSelectedItemPosition();
        String categoryId = categories.get(selectedCategoryPosition).getId();

        Log.d("AddTaskActivity", "Selected category: " + categories.get(selectedCategoryPosition).getName() +
              " (ID: " + categoryId + ")");

        // Get selected priority
        String priority = "medium"; // default
        int selectedPriorityId = radioGroupPriority.getCheckedRadioButtonId();
        if (selectedPriorityId == R.id.radioHigh) {
            priority = "high";
        } else if (selectedPriorityId == R.id.radioMedium) {
            priority = "medium";
        } else if (selectedPriorityId == R.id.radioLow) {
            priority = "low";
        }

        // Create task
        Task newTask = new Task(title, description, categoryId, selectedDeadline, priority);
        if (selectedStartDate != null) {
            newTask.setStartDate(selectedStartDate);
        }
        if (selectedImagePath != null) {
            newTask.setImagePath(selectedImagePath);
        }

        // Gọi API để tạo task
        Log.d("AddTaskActivity", "Bắt đầu tạo task: " + title);

        TaskAPI.createTask(newTask, new TaskAPI.CreateTaskCallback() {
            @Override
            public void onSuccess(Task createdTask) {
                Log.d("AddTaskActivity", "Tạo task thành công! ID: " + createdTask.getId());

                runOnUiThread(() -> {
                    Toast.makeText(AddTaskActivity.this,
                        "Nhiệm vụ đã được tạo thành công!",
                        Toast.LENGTH_SHORT).show();

                    // Tạo Intent result để thông báo task đã được tạo
                    Intent resultIntent = new Intent();
                    resultIntent.putExtra(EXTRA_TASK_CREATED, true);

                    // Set result code để TodoFragment biết cần refresh
                    setResult(RESULT_TASK_CREATED, resultIntent);

                    Log.d("AddTaskActivity", "Đã set result code: " + RESULT_TASK_CREATED);

                    // Return to previous screen
                    finish();
                });
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("AddTaskActivity", "Lỗi tạo task: " + errorMessage);

                runOnUiThread(() -> {
                    Toast.makeText(AddTaskActivity.this,
                        "Lỗi tạo nhiệm vụ: " + errorMessage,
                        Toast.LENGTH_LONG).show();
                });
            }
        });
    }
}
