1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:6:22-77
13
14    <permission
14-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:8:5-32:19
21        android:allowBackup="true"
21-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.MyApp"
31-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:16:9-43
32        android:usesCleartextTraffic="true" >
32-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:17:9-44
33        <activity
33-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:19:9-27:20
34            android:name="com.example.myapp.activities.MainActivity"
34-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:20:13-52
35            android:exported="true" >
35-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:21:13-36
36            <intent-filter>
36-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:22:13-26:29
37                <action android:name="android.intent.action.MAIN" />
37-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:23:17-69
37-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:23:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:25:17-77
39-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:25:27-74
40            </intent-filter>
41        </activity>
42        <activity
42-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:28:9-31:69
43            android:name="com.example.myapp.activities.AddTaskActivity"
43-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:29:13-55
44            android:exported="false"
44-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:30:13-37
45            android:parentActivityName="com.example.myapp.activities.MainActivity" />
45-->D:\TienDuong\MyApp\app\src\main\AndroidManifest.xml:31:13-66
46
47        <provider
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
48            android:name="androidx.startup.InitializationProvider"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
49            android:authorities="com.example.myapp.androidx-startup"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
50            android:exported="false" >
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
51            <meta-data
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.emoji2.text.EmojiCompatInitializer"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
53                android:value="androidx.startup" />
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\04dfed518a5efac2229eb94eadf3271c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
55-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\04dfed518a5efac2229eb94eadf3271c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
56                android:value="androidx.startup" />
56-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\04dfed518a5efac2229eb94eadf3271c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
59                android:value="androidx.startup" />
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
60        </provider>
61
62        <receiver
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
63            android:name="androidx.profileinstaller.ProfileInstallReceiver"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
64            android:directBootAware="false"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
65            android:enabled="true"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
66            android:exported="true"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
67            android:permission="android.permission.DUMP" >
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
69                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
72                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
75                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
78                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
79            </intent-filter>
80        </receiver>
81    </application>
82
83</manifest>
