<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Thêm chủ đề mới"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Category Name -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Tên chủ đề *">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editTextCategoryName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLength="50"
            android:inputType="textCapWords" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Category Description -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Mô tả">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editTextCategoryDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLength="200"
            android:inputType="textCapSentences"
            android:minLines="2"
            android:maxLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Color Selection -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Chọn màu"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="12dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <View
            android:id="@+id/colorBlue"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="#2196F3"
            android:layout_marginEnd="12dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/colorGreen"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="#4CAF50"
            android:layout_marginEnd="12dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/colorOrange"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="#FF9800"
            android:layout_marginEnd="12dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/colorRed"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="#F44336"
            android:layout_marginEnd="12dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/colorPurple"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="#9C27B0"
            android:layout_marginEnd="12dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/colorTeal"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="#009688"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

    <!-- Selected Color Preview -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Màu đã chọn: "
            android:textSize="14sp"
            android:textColor="#666666" />

        <View
            android:id="@+id/selectedColorPreview"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="#2196F3"
            android:layout_marginStart="8dp" />

        <TextView
            android:id="@+id/textSelectedColor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="#2196F3"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/buttonCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hủy"
            android:textColor="#666666"
            android:background="@android:color/transparent"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/buttonSave"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Lưu"
            android:textColor="@android:color/white"
            android:backgroundTint="#4CAF50" />

    </LinearLayout>

</LinearLayout>
