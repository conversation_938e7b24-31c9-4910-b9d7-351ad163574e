<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="12dp"
    android:layout_margin="8dp"
    android:background="@android:drawable/btn_default"
    android:clickable="true"
    android:focusable="true">

    <View
        android:id="@+id/categoryColorIndicator"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@android:color/holo_blue_light"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/textCategoryName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Category"
        android:textSize="12sp"
        android:textColor="@android:color/black"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
