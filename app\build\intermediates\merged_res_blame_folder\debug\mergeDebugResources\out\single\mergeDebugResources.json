[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_item_search_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\item_search_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_dialog_add_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\dialog_add_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_item_search_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\item_search_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_fragment_navbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\fragment_navbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_item_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\item_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_item_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_fragment_todo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\fragment_todo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_activity_test_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\activity_test_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "com.example.myapp-debug-32:/layout_item_subtask.xml.flat", "source": "com.example.myapp-main-34:/layout/item_subtask.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_fragment_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_fragment_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\fragment_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_activity_add_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\activity_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\xml\\backup_rules.xml"}, {"merged": "com.example.myapp-debug-32:/layout_item_task.xml.flat", "source": "com.example.myapp-main-34:/layout/item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-debug-32:\\layout_dialog_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapp-main-34:\\layout\\dialog_search.xml"}]