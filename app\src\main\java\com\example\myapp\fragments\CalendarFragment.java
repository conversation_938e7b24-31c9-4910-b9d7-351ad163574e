package com.example.myapp.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CalendarView;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapp.R;
import com.example.myapp.adapters.TaskAdapter;
import com.example.myapp.models.Task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class CalendarFragment extends Fragment {

    private CalendarView calendarView;
    private TextView textSelectedDate, textNoTasksForDate;
    private RecyclerView recyclerViewCalendarTasks;
    private ImageButton buttonMenu, buttonToday;
    private LinearLayout navTasks, navCalendar, navProfile;

    private List<Task> allTasks;
    private List<Task> tasksForSelectedDate;
    private TaskAdapter taskAdapter;
    private SimpleDateFormat dateFormat;
    private Date selectedDate;

    private OnNavigationClickListener navigationListener;

    public interface OnNavigationClickListener {
        void onTasksClick();
        void onCalendarClick();
        void onProfileClick();
        void onMenuClick();
    }

    public CalendarFragment() {
        // Required empty public constructor
    }

    public static CalendarFragment newInstance() {
        return new CalendarFragment();
    }

    public void setOnNavigationClickListener(OnNavigationClickListener listener) {
        this.navigationListener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        allTasks = new ArrayList<>();
        tasksForSelectedDate = new ArrayList<>();
        dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        selectedDate = new Date(); // Today
        
        loadSampleTasks();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_calendar, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupRecyclerView();
        setupClickListeners();
        updateTasksForSelectedDate();
    }

    private void initViews(View view) {
        calendarView = view.findViewById(R.id.calendarView);
        textSelectedDate = view.findViewById(R.id.textSelectedDate);
        textNoTasksForDate = view.findViewById(R.id.textNoTasksForDate);
        recyclerViewCalendarTasks = view.findViewById(R.id.recyclerViewCalendarTasks);
        buttonMenu = view.findViewById(R.id.buttonMenu);
        buttonToday = view.findViewById(R.id.buttonToday);
        navTasks = view.findViewById(R.id.navTasks);
        navCalendar = view.findViewById(R.id.navCalendar);
        navProfile = view.findViewById(R.id.navProfile);
    }

    private void setupRecyclerView() {
        taskAdapter = new TaskAdapter(tasksForSelectedDate, new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskToggle(Task task, int position) {
                // Handle task toggle
            }

            @Override
            public void onTaskClick(Task task, int position) {
                // Handle task click
            }

            @Override
            public void onTaskDelete(Task task, int position) {
                // Handle task delete
            }
        });
        
        recyclerViewCalendarTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewCalendarTasks.setAdapter(taskAdapter);
    }

    private void setupClickListeners() {
        calendarView.setOnDateChangeListener((view, year, month, dayOfMonth) -> {
            Calendar calendar = Calendar.getInstance();
            calendar.set(year, month, dayOfMonth);
            selectedDate = calendar.getTime();
            updateTasksForSelectedDate();
        });

        buttonToday.setOnClickListener(v -> {
            selectedDate = new Date();
            calendarView.setDate(selectedDate.getTime());
            updateTasksForSelectedDate();
        });

        navTasks.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onTasksClick();
            }
        });

        navCalendar.setOnClickListener(v -> {
            // Already on calendar screen
        });

        navProfile.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onProfileClick();
            }
        });
    }

    private void loadSampleTasks() {
        // Add sample tasks with different dates
        Calendar calendar = Calendar.getInstance();
        
        // Today's tasks
        Task task1 = new Task("Họp team", "Họp review dự án", "1", new Date(), "high");
        allTasks.add(task1);
        
        // Tomorrow's tasks
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        Task task2 = new Task("Viết báo cáo", "Hoàn thành báo cáo tháng", "1", calendar.getTime(), "medium");
        allTasks.add(task2);
    }

    private void updateTasksForSelectedDate() {
        tasksForSelectedDate.clear();
        
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String selectedDateStr = dayFormat.format(selectedDate);
        
        for (Task task : allTasks) {
            if (task.getDeadline() != null) {
                String taskDateStr = dayFormat.format(task.getDeadline());
                if (selectedDateStr.equals(taskDateStr)) {
                    tasksForSelectedDate.add(task);
                }
            }
        }
        
        // Update UI
        textSelectedDate.setText("Ngày: " + dateFormat.format(selectedDate));
        
        if (tasksForSelectedDate.isEmpty()) {
            recyclerViewCalendarTasks.setVisibility(View.GONE);
            textNoTasksForDate.setVisibility(View.VISIBLE);
        } else {
            recyclerViewCalendarTasks.setVisibility(View.VISIBLE);
            textNoTasksForDate.setVisibility(View.GONE);
        }
        
        if (taskAdapter != null) {
            taskAdapter.notifyDataSetChanged();
        }
    }
}
