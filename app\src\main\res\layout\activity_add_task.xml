<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#2196F3"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/buttonBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_revert"

            android:contentDescription="Back"
            android:padding="12dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Thêm nhiệm vụ mới"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/buttonSave"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="Lưu"
            android:textColor="@android:color/white"
            android:background="@android:drawable/btn_default"
            android:backgroundTint="#4CAF50"
            android:minWidth="0dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

    </LinearLayout>

    <!-- Form Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Title -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Tiêu đề *">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLength="100"
                    android:inputType="textCapSentences" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Description -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Mô tả">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLength="500"
                    android:inputType="textMultiLine|textCapSentences"
                    android:minLines="3"
                    android:maxLines="5" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Category -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Chủ đề *"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/spinnerCategory"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/btn_dropdown" />

            <!-- Priority -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Độ ưu tiên"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <RadioGroup
                android:id="@+id/radioGroupPriority"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <RadioButton
                    android:id="@+id/radioLow"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Thấp"
                    android:textColor="#4CAF50" />

                <RadioButton
                    android:id="@+id/radioMedium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Trung bình"
                    android:textColor="#FF9800"
                    android:checked="true" />

                <RadioButton
                    android:id="@+id/radioHigh"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Cao"
                    android:textColor="#F44336" />

            </RadioGroup>

            <!-- Start Date -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Ngày bắt đầu"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/textStartDate"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="Chọn ngày bắt đầu"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:gravity="center_vertical"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:background="@android:drawable/edit_text" />

                <Button
                    android:id="@+id/buttonSelectStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Chọn"
                    android:textColor="@android:color/white"
                    android:backgroundTint="#2196F3" />

            </LinearLayout>

            <!-- Deadline -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Hạn chót *"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/textDeadline"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="Chọn hạn chót"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:gravity="center_vertical"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:background="@android:drawable/edit_text" />

                <Button
                    android:id="@+id/buttonSelectDeadline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Chọn"
                    android:textColor="@android:color/white"
                    android:backgroundTint="#2196F3" />

            </LinearLayout>

            <!-- Image -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Hình ảnh"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="24dp">

                <ImageView
                    android:id="@+id/imagePreview"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:background="@android:drawable/btn_default"
                    android:scaleType="centerCrop"
                    android:src="@android:drawable/ic_menu_camera"
                    android:contentDescription="Image preview" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <Button
                        android:id="@+id/buttonSelectImage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Chọn hình ảnh"
                        android:textColor="@android:color/white"
                        android:backgroundTint="#9C27B0"
                        android:layout_marginBottom="8dp" />

                    <Button
                        android:id="@+id/buttonRemoveImage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Xóa hình ảnh"
                        android:textColor="@android:color/white"
                        android:backgroundTint="#F44336"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
