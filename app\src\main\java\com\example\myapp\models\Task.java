package com.example.myapp.models;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;

public class Task {
    private String _id;
    private String title;
    private String description;
    private String categoryId;
    private Date startDate;
    private Date deadline;
    private boolean isCompleted;
    private Date completedAt;
    private String priority; // low, medium, high
    private String imagePath;
    private String userId;
    private Date createdAt;
    private Date updatedAt;

    // SubTask related fields
    private List<SubTask> subTasks;
    private int totalSubTasks;
    private int completedSubTasks;

    // Constructors
    public Task() {
        this.subTasks = new ArrayList<>();
        this.totalSubTasks = 0;
        this.completedSubTasks = 0;
    }

    public Task(String title, String description, String categoryId, Date deadline, String priority) {
        this.title = title;
        this.description = description;
        this.categoryId = categoryId;
        this.deadline = deadline;
        this.priority = priority;
        this.isCompleted = false;
        this.startDate = new Date();
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.subTasks = new ArrayList<>();
        this.totalSubTasks = 0;
        this.completedSubTasks = 0;
    }

    // Getters and Setters
    public String getId() {
        return _id;
    }

    public void setId(String id) {
        this._id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        if (completed) {
            this.completedAt = new Date();
        } else {
            this.completedAt = null;
        }
        this.updatedAt = new Date();
    }

    public Date getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(Date completedAt) {
        this.completedAt = completedAt;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    public String getPriorityColor() {
        switch (priority) {
            case "high":
                return "#FF5722"; // Red
            case "medium":
                return "#FF9800"; // Orange
            case "low":
                return "#4CAF50"; // Green
            default:
                return "#9E9E9E"; // Gray
        }
    }

    public boolean isOverdue() {
        if (deadline == null || isCompleted) return false;
        return new Date().after(deadline);
    }

    // SubTask related methods
    public List<SubTask> getSubTasks() {
        return subTasks;
    }

    public void setSubTasks(List<SubTask> subTasks) {
        this.subTasks = subTasks;
        if (subTasks != null) {
            this.totalSubTasks = subTasks.size();
            this.completedSubTasks = 0;
            for (SubTask subTask : subTasks) {
                if (subTask.isCompleted()) {
                    this.completedSubTasks++;
                }
            }
        }
    }

    public int getTotalSubTasks() {
        return totalSubTasks;
    }

    public void setTotalSubTasks(int totalSubTasks) {
        this.totalSubTasks = totalSubTasks;
    }

    public int getCompletedSubTasks() {
        return completedSubTasks;
    }

    public void setCompletedSubTasks(int completedSubTasks) {
        this.completedSubTasks = completedSubTasks;
    }

    public int getCompletionPercentage() {
        if (totalSubTasks == 0) return 0;
        return Math.round((float) completedSubTasks / totalSubTasks * 100);
    }

    public boolean hasSubTasks() {
        return subTasks != null && !subTasks.isEmpty();
    }
}
