package com.example.myapp.adapters;

import android.graphics.Color;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapp.R;
import com.example.myapp.models.Task;
import com.example.myapp.models.SubTask;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class TaskAdapter extends RecyclerView.Adapter<TaskAdapter.TaskViewHolder> {

    private List<Task> taskList;
    private OnTaskClickListener listener;
    private SimpleDateFormat dateFormat;

    public interface OnTaskClickListener {
        void onTaskToggle(Task task, int position);
        void onTaskClick(Task task, int position);
        void onTaskDelete(Task task, int position);
        void onSubTaskToggle(Task task, SubTask subTask, int taskPosition, int subTaskPosition);
    }

    public TaskAdapter(List<Task> taskList, OnTaskClickListener listener) {
        this.taskList = taskList;
        this.listener = listener;
        this.dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    }

    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_task, parent, false);
        return new TaskViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        Task task = taskList.get(position);
        holder.bind(task, position);
    }

    @Override
    public int getItemCount() {
        return taskList.size();
    }

    public class TaskViewHolder extends RecyclerView.ViewHolder {
        private TextView textTaskTitle, textTaskDescription, textCategoryName, textDeadline, textOverdue;
        private TextView textCompletionPercentage;
        private ProgressBar progressBarCompletion;
        private View priorityIndicator, categoryColorDot;
        private ImageButton buttonTaskMenu;
        private RecyclerView recyclerViewSubTasks;
        private SubTaskAdapter subTaskAdapter;

        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            textTaskTitle = itemView.findViewById(R.id.textTaskTitle);
            textTaskDescription = itemView.findViewById(R.id.textTaskDescription);
            textCategoryName = itemView.findViewById(R.id.textCategoryName);
            textDeadline = itemView.findViewById(R.id.textDeadline);
            textOverdue = itemView.findViewById(R.id.textOverdue);
            textCompletionPercentage = itemView.findViewById(R.id.textCompletionPercentage);
            progressBarCompletion = itemView.findViewById(R.id.progressBarCompletion);
            priorityIndicator = itemView.findViewById(R.id.priorityIndicator);
            categoryColorDot = itemView.findViewById(R.id.categoryColorDot);
            buttonTaskMenu = itemView.findViewById(R.id.buttonTaskMenu);
            recyclerViewSubTasks = itemView.findViewById(R.id.recyclerViewSubTasks);

            // Setup SubTasks RecyclerView
            recyclerViewSubTasks.setLayoutManager(new LinearLayoutManager(itemView.getContext()));
            recyclerViewSubTasks.setNestedScrollingEnabled(false);
        }

        public void bind(Task task, int position) {
            // Set task title
            textTaskTitle.setText(task.getTitle());

            // Set task description
            if (task.getDescription() != null && !task.getDescription().trim().isEmpty()) {
                textTaskDescription.setText(task.getDescription());
                textTaskDescription.setVisibility(View.VISIBLE);
            } else {
                textTaskDescription.setVisibility(View.GONE);
            }

            // Set completion progress
            int completionPercentage = task.getCompletionPercentage();
            progressBarCompletion.setProgress(completionPercentage);
            textCompletionPercentage.setText(completionPercentage + "%");

            // Apply completion styling to title
            if (task.isCompleted()) {
                textTaskTitle.setPaintFlags(textTaskTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                textTaskTitle.setAlpha(0.6f);
                if (textTaskDescription.getVisibility() == View.VISIBLE) {
                    textTaskDescription.setPaintFlags(textTaskDescription.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                    textTaskDescription.setAlpha(0.6f);
                }
            } else {
                textTaskTitle.setPaintFlags(textTaskTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                textTaskTitle.setAlpha(1.0f);
                if (textTaskDescription.getVisibility() == View.VISIBLE) {
                    textTaskDescription.setPaintFlags(textTaskDescription.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                    textTaskDescription.setAlpha(1.0f);
                }
            }

            // Apply strikethrough effect if completed
            if (task.isCompleted()) {
                textTaskTitle.setPaintFlags(textTaskTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                textTaskTitle.setAlpha(0.6f);
                if (textTaskDescription.getVisibility() == View.VISIBLE) {
                    textTaskDescription.setPaintFlags(textTaskDescription.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                    textTaskDescription.setAlpha(0.6f);
                }
            } else {
                textTaskTitle.setPaintFlags(textTaskTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                textTaskTitle.setAlpha(1.0f);
                if (textTaskDescription.getVisibility() == View.VISIBLE) {
                    textTaskDescription.setPaintFlags(textTaskDescription.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                    textTaskDescription.setAlpha(1.0f);
                }
            }

            // Set priority indicator color
            try {
                int priorityColor = Color.parseColor(task.getPriorityColor());
                priorityIndicator.setBackgroundColor(priorityColor);
            } catch (IllegalArgumentException e) {
                priorityIndicator.setBackgroundColor(Color.parseColor("#9E9E9E"));
            }

            // Set category (placeholder for now)
            textCategoryName.setText("Công việc"); // TODO: Get actual category name
            categoryColorDot.setBackgroundColor(Color.parseColor("#2196F3")); // TODO: Get actual category color

            // Setup SubTasks
            if (task.hasSubTasks() && task.getSubTasks() != null && !task.getSubTasks().isEmpty()) {
                recyclerViewSubTasks.setVisibility(View.VISIBLE);

                if (subTaskAdapter == null) {
                    subTaskAdapter = new SubTaskAdapter(task.getSubTasks(), new SubTaskAdapter.OnSubTaskClickListener() {
                        @Override
                        public void onSubTaskToggle(SubTask subTask, int subTaskPosition) {
                            if (listener != null) {
                                listener.onSubTaskToggle(task, subTask, position, subTaskPosition);
                            }
                        }

                        @Override
                        public void onSubTaskClick(SubTask subTask, int subTaskPosition) {
                            // Handle subtask click if needed
                        }
                    });
                    recyclerViewSubTasks.setAdapter(subTaskAdapter);
                } else {
                    subTaskAdapter.updateSubTasks(task.getSubTasks());
                }
            } else {
                recyclerViewSubTasks.setVisibility(View.GONE);
            }

            // Set deadline
            if (task.getDeadline() != null) {
                textDeadline.setText(dateFormat.format(task.getDeadline()));
            } else {
                textDeadline.setText("Không có hạn");
            }

            // Show overdue indicator
            if (task.isOverdue()) {
                textOverdue.setVisibility(View.VISIBLE);
                textDeadline.setTextColor(Color.parseColor("#F44336"));
            } else {
                textOverdue.setVisibility(View.GONE);
                textDeadline.setTextColor(Color.parseColor("#666666"));
            }

            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskClick(task, position);
                }
            });

            buttonTaskMenu.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskDelete(task, position);
                }
            });
        }
    }
}
