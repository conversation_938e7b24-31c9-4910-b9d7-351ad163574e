const express = require('express');
const {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  toggleTaskStatus,
  deleteTask,
  getTaskStats,
  upload
} = require('../controllers/taskController');

const {
  getSubTasks,
  createSubTask,
  updateSubTask,
  toggleSubTask,
  deleteSubTask,
  reorderSubTasks
} = require('../controllers/subTaskController');

const router = express.Router();

// Task routes
router.post('/', createTask);
router.get('/', getTasks);
router.get('/stats', getTaskStats);
router.get('/:taskId', getTaskById);
router.put('/:taskId', upload.single('image'), updateTask);
router.patch('/:taskId/toggle', toggleTaskStatus);
router.delete('/:taskId', deleteTask);

// SubTask routes (nested under tasks)
router.get('/:taskId/subtasks', getSubTasks);
router.post('/:taskId/subtasks', createSubTask);
router.put('/:taskId/subtasks/:subTaskId', updateSubTask);
router.patch('/:taskId/subtasks/:subTaskId/toggle', toggleSubTask);
router.delete('/:taskId/subtasks/:subTaskId', deleteSubTask);
router.post('/:taskId/subtasks/reorder', reorderSubTasks);

module.exports = router;