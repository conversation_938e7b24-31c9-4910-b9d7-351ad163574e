const Task = require('../models/Task');
const SubTask = require('../models/SubTask');
const Category = require('../models/Category');
const multer = require('multer');
const path = require('path');

// C<PERSON>u hình <PERSON>lter để lưu ảnh
const storage = multer.diskStorage({
  destination: './uploads/',
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});
const upload = multer({ storage });

// Tạo task mới
const createTask = async (req, res) => {
  const { title, description, categoryId, startDate, deadline, priority, userId, subTasks } = req.body;
  console.log("🚀 ~ createTask ~ body:", req.body)
  const imagePath = req.body.imagePath || null;

  try {
    if (!title || !categoryId || !deadline || !userId) {
      console.log("🚀 ~ createTask ~ userId:", userId)
      console.log("🚀 ~ createTask ~ deadline:", deadline)
      console.log("🚀 ~ createTask ~ categoryId:", categoryId)
      console.log("🚀 ~ createTask ~ title:", title)
      return res.status(400).json({ message: 'Title, categoryId, deadline, and userId are required' });
    }

    // Kiểm tra category có tồn tại không
    const category = await Category.findById(categoryId);

    if (!category) {
      return res.status(400).json({ message: 'Category not found' });
    }

    // Tạo task trước
    const task = new Task({
      title: title.trim(),
      description,
      categoryId,
      startDate: startDate || Date.now(),
      deadline,
      priority: priority || 'medium',
      imagePath,
      userId: userId,
      subTasks: [],
      totalSubTasks: 0,
      completedSubTasks: 0
    });

    await task.save();

    // Tạo subtasks nếu có
    const createdSubTasks = [];
    if (subTasks && Array.isArray(subTasks) && subTasks.length > 0) {
      for (let i = 0; i < subTasks.length; i++) {
        const subTaskData = subTasks[i];
        const subTask = new SubTask({
          title: subTaskData.title.trim(),
          description: subTaskData.description || '',
          order: i,
          isCompleted: false
        });

        await subTask.save();
        createdSubTasks.push(subTask._id);
      }
    } else {
      // Nếu không có subtasks, tạo 1 subtask mặc định từ description
      const defaultSubTask = new SubTask({
        title: description || title,
        description: '',
        order: 0,
        isCompleted: false
      });

      await defaultSubTask.save();
      createdSubTasks.push(defaultSubTask._id);
    }

    // Cập nhật task với subtasks
    task.subTasks = createdSubTasks;
    task.totalSubTasks = createdSubTasks.length;
    task.completedSubTasks = 0;

    await task.save();

    // Populate category info và subtasks
    await task.populate('categoryId', 'name color icon');
    await task.populate('subTasks');

    res.status(201).json(task);
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Lấy danh sách tasks với filter, search, pagination
const getTasks = async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    categoryId, 
    search, 
    isCompleted, 
    priority,
    userId,
    sortBy = 'deadline', 
    order = 'asc' 
  } = req.query;

  try {
    // Xây dựng query
    const query = {};
    
    // Filter theo userId
    if (userId) {
      query.userId = userId;
    }
    
    // Filter theo category
    if (categoryId && categoryId !== 'all') {
      query.categoryId = categoryId;
    }
    
    // Filter theo trạng thái hoàn thành
    if (isCompleted !== undefined) {
      query.isCompleted = isCompleted === 'true';
    }
    
    // Filter theo priority
    if (priority) {
      query.priority = priority;
    }
    
    // Search trong title và description
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Sắp xếp
    const sortOrder = order === 'desc' ? -1 : 1;
    const sortOptions = { [sortBy]: sortOrder };

    // Thực hiện query với pagination
    const tasks = await Task.find(query)
      .populate('categoryId', 'name color icon')
      .populate('subTasks')
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(Number(limit));

    const total = await Task.countDocuments(query);

    // Debug logging
    console.log(`=== getTasks Debug ===`);
    console.log(`Found ${tasks.length} tasks for user ${userId}`);
    tasks.forEach((task, index) => {
      console.log(`Task ${index + 1}: ${task.title}`);
      console.log(`  - ID: ${task._id}`);
      console.log(`  - SubTasks count: ${task.subTasks.length}`);
      console.log(`  - SubTasks IDs: [${task.subTasks.map(st => st._id).join(', ')}]`);
      if (task.subTasks.length > 0) {
        task.subTasks.forEach((st, stIndex) => {
          console.log(`    SubTask ${stIndex + 1}: ${st.title} (ID: ${st._id}, Completed: ${st.isCompleted})`);
        });
      }
    });

    res.json({
      tasks,
      total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Lấy task theo ID
const getTaskById = async (req, res) => {
  const { taskId } = req.params;
  const { userId } = req.query;
  
  try {
    const query = { _id: taskId };
    if (userId) query.userId = userId;
    
    const task = await Task.findOne(query)
      .populate('categoryId', 'name color icon')
      .populate('subTasks');
    
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json(task);
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Cập nhật task
const updateTask = async (req, res) => {
  const { taskId } = req.params;
  const { title, description, categoryId, startDate, deadline, priority, userId } = req.body;
  const imagePath = req.file ? req.file.path : null;

  try {
    const query = { _id: taskId };
    if (userId) query.userId = userId;
    
    const task = await Task.findOne(query);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Kiểm tra category nếu có thay đổi
    if (categoryId && categoryId !== task.categoryId.toString()) {
      const category = await Category.findById(categoryId);
      
      if (!category) {
        return res.status(400).json({ message: 'Category not found' });
      }
    }

    // Cập nhật các field
    if (title) task.title = title.trim();
    if (description !== undefined) task.description = description;
    if (categoryId) task.categoryId = categoryId;
    if (startDate) task.startDate = startDate;
    if (deadline) task.deadline = deadline;
    if (priority) task.priority = priority;
    if (imagePath) task.imagePath = imagePath;

    await task.save();
    await task.populate('categoryId', 'name color icon');
    await task.populate('subTasks');

    res.json(task);
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Toggle trạng thái hoàn thành
const toggleTaskStatus = async (req, res) => {
  const { taskId } = req.params;
  const { userId } = req.query;
  
  try {
    const query = { _id: taskId };
    if (userId) query.userId = userId;
    
    const task = await Task.findOne(query);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Không cho phép toggle task trực tiếp nữa
    // Task completion sẽ được tự động tính dựa trên subtasks
    return res.status(400).json({
      message: 'Cannot toggle task completion directly. Please complete all subtasks instead.'
    });
  } catch (error) {
    console.error('Toggle task status error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Xóa task
const deleteTask = async (req, res) => {
  const { taskId } = req.params;
  const { userId } = req.query;
  
  try {
    const query = { _id: taskId };
    if (userId) query.userId = userId;
    
    const task = await Task.findOne(query);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Xóa tất cả subtasks trước
    if (task.subTasks && task.subTasks.length > 0) {
      await SubTask.deleteMany({ _id: { $in: task.subTasks } });
    }

    // Xóa task
    await Task.findByIdAndDelete(task._id);

    res.json({ message: 'Task and all subtasks deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Thống kê tasks
const getTaskStats = async (req, res) => {
  const { userId } = req.query;

  try {
    console.log('🔍 getTaskStats userId:', userId);

    const matchQuery = {};
    if (userId) {
      // Convert string to ObjectId if needed
      const mongoose = require('mongoose');
      matchQuery.userId = new mongoose.Types.ObjectId(userId);
    }

    console.log('🔍 matchQuery:', matchQuery);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));

    const stats = await Task.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalTasks: { $sum: 1 },
          completedTasks: {
            $sum: { $cond: [{ $eq: ['$isCompleted', true] }, 1, 0] }
          },
          pendingTasks: {
            $sum: { $cond: [{ $eq: ['$isCompleted', false] }, 1, 0] }
          },
          overdueTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$isCompleted', false] },
                    { $lt: ['$deadline', now] }
                  ]
                },
                1,
                0
              ]
            }
          },
          todayTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$deadline', today] },
                    { $lt: ['$deadline', new Date(today.getTime() + 24 * 60 * 60 * 1000)] }
                  ]
                },
                1,
                0
              ]
            }
          },
          thisWeekTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$deadline', weekStart] },
                    { $lt: ['$deadline', new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    const result = stats[0] || {
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      overdueTasks: 0,
      todayTasks: 0,
      thisWeekTasks: 0
    };

    res.json(result);
  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  toggleTaskStatus,
  deleteTask,
  getTaskStats,
  upload
};