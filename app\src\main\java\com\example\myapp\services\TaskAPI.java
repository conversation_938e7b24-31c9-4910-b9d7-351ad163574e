package com.example.myapp.services;

import android.os.AsyncTask;
import android.util.Log;

import com.example.myapp.models.Task;
import com.example.myapp.models.SubTask;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Class chuyên xử lý API liên quan đến Tasks
 */
public class TaskAPI {
    
    private static final String TAG = "TaskAPI";
    // Thay IP này nếu dùng thiết bị thật
    private static final String BASE_URL = "http://********:5000/api";
    
    /**
     * Interface để trả kết quả về TodoFragment
     */
    public interface TaskCallback {
        void onSuccess(List<Task> tasks);
        void onError(String errorMessage);
    }

    /**
     * Interface để trả kết quả tạo task
     */
    public interface CreateTaskCallback {
        void onSuccess(Task task);
        void onError(String errorMessage);
    }
    
    /**
     * Gọi API lấy tất cả tasks của user
     * @param userId ID của user
     * @param callback Interface để trả kết quả
     */
    public static void getTasks(String userId, TaskCallback callback) {
        new GetTasksTask(callback).execute(userId, null);
    }

    /**
     * Gọi API lấy tasks theo category
     * @param userId ID của user
     * @param categoryId ID của category
     * @param callback Interface để trả kết quả
     */
    public static void getTasksByCategory(String userId, String categoryId, TaskCallback callback) {
        new GetTasksTask(callback).execute(userId, categoryId);
    }

    /**
     * Gọi API tạo task mới
     * @param task Task object cần tạo
     * @param callback Interface để trả kết quả
     */
    public static void createTask(Task task, CreateTaskCallback callback) {
        new CreateTaskTask(callback).execute(task);
    }
    
    /**
     * AsyncTask gọi API trong background
     */
    private static class GetTasksTask extends AsyncTask<String, Void, List<Task>> {
        
        private TaskCallback callback;
        private String errorMessage;
        
        public GetTasksTask(TaskCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected List<Task> doInBackground(String... params) {
            String userId = params[0];
            String categoryId = params.length > 1 ? params[1] : null;
            List<Task> tasks = new ArrayList<>();

            try {
                // Tạo URL với userId và categoryId (nếu có)
                String urlString = BASE_URL + "/tasks?userId=" + userId;
                if (categoryId != null && !categoryId.isEmpty()) {
                    urlString += "&categoryId=" + categoryId;
                }
                Log.d(TAG, "Gọi API: " + urlString);
                
                // Tạo HTTP connection
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                
                // Kiểm tra response
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Đọc response
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream())
                    );
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String jsonResponse = response.toString();
                    Log.d(TAG, "Response: " + jsonResponse);
                    
                    // Parse JSON
                    tasks = parseJsonToTasks(jsonResponse);
                    
                } else {
                    errorMessage = "Lỗi HTTP: " + responseCode;
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                errorMessage = "Lỗi kết nối: " + e.getMessage();
                Log.e(TAG, "Lỗi API", e);
            }
            
            return tasks;
        }
        
        @Override
        protected void onPostExecute(List<Task> tasks) {
            if (callback != null) {
                if (errorMessage != null) {
                    callback.onError(errorMessage);
                } else {
                    callback.onSuccess(tasks);
                }
            }
        }
    }

    /**
     * AsyncTask để tạo task trong background
     */
    private static class CreateTaskTask extends AsyncTask<Task, Void, Task> {

        private CreateTaskCallback callback;
        private String errorMessage;

        public CreateTaskTask(CreateTaskCallback callback) {
            this.callback = callback;
        }

        @Override
        protected Task doInBackground(Task... params) {
            Task taskToCreate = params[0];
            Task createdTask = null;

            try {
                // Tạo URL
                String urlString = BASE_URL + "/tasks";
                Log.d(TAG, "Gọi API POST: " + urlString);

                // Tạo HTTP connection
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);

                // Tạo JSON body
                String jsonBody = createTaskJson(taskToCreate);
                Log.d(TAG, "Request Body: " + jsonBody);

                // Gửi request body
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonBody.getBytes("utf-8");
                    os.write(input, 0, input.length);
                }

                // Kiểm tra response
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);

                if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                    // Đọc response
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream())
                    );
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String jsonResponse = response.toString();
                    Log.d(TAG, "Response: " + jsonResponse);

                    // Parse response thành Task object
                    createdTask = parseJsonToTask(jsonResponse);

                } else {
                    // Đọc error response
                    BufferedReader errorReader = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream())
                    );
                    StringBuilder errorResponse = new StringBuilder();
                    String line;

                    while ((line = errorReader.readLine()) != null) {
                        errorResponse.append(line);
                    }
                    errorReader.close();

                    errorMessage = "HTTP " + responseCode + ": " + errorResponse.toString();
                    Log.e(TAG, errorMessage);
                }

                connection.disconnect();

            } catch (Exception e) {
                errorMessage = "Lỗi kết nối: " + e.getMessage();
                Log.e(TAG, "Lỗi tạo task", e);
            }

            return createdTask;
        }

        @Override
        protected void onPostExecute(Task createdTask) {
            if (callback != null) {
                if (errorMessage != null) {
                    callback.onError(errorMessage);
                } else {
                    callback.onSuccess(createdTask);
                }
            }
        }
    }

    /**
     * Parse JSON response thành List<Task>
     * @param jsonString JSON string từ server
     * @return List<Task>
     */
    private static List<Task> parseJsonToTasks(String jsonString) {
        List<Task> tasks = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        
        try {
            JSONObject jsonResponse = new JSONObject(jsonString);
            JSONArray tasksArray = jsonResponse.getJSONArray("tasks");
            
            for (int i = 0; i < tasksArray.length(); i++) {
                JSONObject taskJson = tasksArray.getJSONObject(i);
                
                Task task = new Task();
                
                // Parse thông tin cơ bản
                task.setId(taskJson.optString("_id"));
                task.setTitle(taskJson.optString("title"));
                task.setDescription(taskJson.optString("description"));
                task.setCompleted(taskJson.optBoolean("isCompleted", false));
                task.setPriority(taskJson.optString("priority", "medium"));
                task.setUserId(taskJson.optString("userId"));
                
                // Parse categoryId - có thể là string hoặc object
                if (taskJson.has("categoryId")) {
                    Object categoryIdObj = taskJson.get("categoryId");
                    if (categoryIdObj instanceof String) {
                        // Nếu là string
                        task.setCategoryId((String) categoryIdObj);
                    } else if (categoryIdObj instanceof JSONObject) {
                        // Nếu là object, lấy _id
                        JSONObject categoryObj = (JSONObject) categoryIdObj;
                        task.setCategoryId(categoryObj.optString("_id"));
                    }
                }
                
                // Parse dates
                try {
                    if (taskJson.has("startDate") && !taskJson.isNull("startDate")) {
                        Date startDate = dateFormat.parse(taskJson.getString("startDate"));
                        task.setStartDate(startDate);
                    }
                    
                    if (taskJson.has("deadline") && !taskJson.isNull("deadline")) {
                        Date deadline = dateFormat.parse(taskJson.getString("deadline"));
                        task.setDeadline(deadline);
                    }
                    
                    if (taskJson.has("createdAt") && !taskJson.isNull("createdAt")) {
                        Date createdAt = dateFormat.parse(taskJson.getString("createdAt"));
                        task.setCreatedAt(createdAt);
                    }
                    
                    if (taskJson.has("updatedAt") && !taskJson.isNull("updatedAt")) {
                        Date updatedAt = dateFormat.parse(taskJson.getString("updatedAt"));
                        task.setUpdatedAt(updatedAt);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Lỗi parse date cho task: " + task.getTitle(), e);
                }

                // Parse SubTasks
                if (taskJson.has("subTasks") && !taskJson.isNull("subTasks")) {
                    JSONArray subTasksArray = taskJson.getJSONArray("subTasks");
                    List<SubTask> subTasks = new ArrayList<>();

                    for (int j = 0; j < subTasksArray.length(); j++) {
                        JSONObject subTaskJson = subTasksArray.getJSONObject(j);

                        SubTask subTask = new SubTask();
                        String subTaskId = subTaskJson.optString("_id");
                        subTask.set_id(subTaskId);
                        subTask.setTitle(subTaskJson.optString("title"));
                        subTask.setDescription(subTaskJson.optString("description"));
                        subTask.setCompleted(subTaskJson.optBoolean("isCompleted", false));
                        subTask.setOrder(subTaskJson.optInt("order", 0));

                        Log.d(TAG, "Parsing SubTask - ID: '" + subTaskId + "', Title: '" + subTask.getTitle() + "', Completed: " + subTask.isCompleted());

                        // Parse dates for subtask
                        try {
                            if (subTaskJson.has("createdAt") && !subTaskJson.isNull("createdAt")) {
                                Date createdAt = dateFormat.parse(subTaskJson.getString("createdAt"));
                                subTask.setCreatedAt(createdAt);
                            }

                            if (subTaskJson.has("updatedAt") && !subTaskJson.isNull("updatedAt")) {
                                Date updatedAt = dateFormat.parse(subTaskJson.getString("updatedAt"));
                                subTask.setUpdatedAt(updatedAt);
                            }

                            if (subTaskJson.has("completedAt") && !subTaskJson.isNull("completedAt")) {
                                Date completedAt = dateFormat.parse(subTaskJson.getString("completedAt"));
                                subTask.setCompletedAt(completedAt);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Lỗi parse date cho subtask: " + subTask.getTitle(), e);
                        }

                        subTasks.add(subTask);
                        Log.d(TAG, "Parsed subtask: " + subTask.getTitle() + " - Completed: " + subTask.isCompleted());
                    }

                    task.setSubTasks(subTasks);
                }

                // Parse SubTask statistics
                task.setTotalSubTasks(taskJson.optInt("totalSubTasks", 0));
                task.setCompletedSubTasks(taskJson.optInt("completedSubTasks", 0));

                tasks.add(task);
                Log.d(TAG, "Parsed task: " + task.getTitle() + " với " + task.getTotalSubTasks() + " subtasks (" + task.getCompletedSubTasks() + " hoàn thành)");
            }
            
            Log.d(TAG, "Tổng số tasks parsed: " + tasks.size());
            
        } catch (Exception e) {
            Log.e(TAG, "Lỗi parse JSON tasks", e);
        }
        
        return tasks;
    }

    /**
     * Tạo JSON string từ Task object
     */
    private static String createTaskJson(Task task) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", task.getTitle());
            jsonObject.put("description", task.getDescription());
            jsonObject.put("categoryId", task.getCategoryId());
            jsonObject.put("priority", task.getPriority());
            jsonObject.put("userId", "68635f9b6f882821ae2a315c"); // UserId thật từ database

            // Format dates
            if (task.getStartDate() != null) {
                jsonObject.put("startDate", dateFormat.format(task.getStartDate()));
            }

            if (task.getDeadline() != null) {
                jsonObject.put("deadline", dateFormat.format(task.getDeadline()));
            }

            return jsonObject.toString();

        } catch (Exception e) {
            Log.e(TAG, "Lỗi tạo JSON", e);
            return "{}";
        }
    }

    /**
     * Parse JSON response thành Task object
     */
    private static Task parseJsonToTask(String jsonString) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());

        try {
            JSONObject jsonObject = new JSONObject(jsonString);

            Task task = new Task();
            task.setId(jsonObject.optString("_id"));
            task.setTitle(jsonObject.optString("title"));
            task.setDescription(jsonObject.optString("description"));
            task.setCategoryId(jsonObject.optString("categoryId"));
            task.setPriority(jsonObject.optString("priority"));
            task.setUserId(jsonObject.optString("userId"));
            task.setCompleted(jsonObject.optBoolean("isCompleted", false));

            // Parse dates
            try {
                if (jsonObject.has("startDate") && !jsonObject.isNull("startDate")) {
                    Date startDate = dateFormat.parse(jsonObject.getString("startDate"));
                    task.setStartDate(startDate);
                }

                if (jsonObject.has("deadline") && !jsonObject.isNull("deadline")) {
                    Date deadline = dateFormat.parse(jsonObject.getString("deadline"));
                    task.setDeadline(deadline);
                }

                if (jsonObject.has("createdAt") && !jsonObject.isNull("createdAt")) {
                    Date createdAt = dateFormat.parse(jsonObject.getString("createdAt"));
                    task.setCreatedAt(createdAt);
                }
            } catch (Exception e) {
                Log.e(TAG, "Lỗi parse date", e);
            }

            Log.d(TAG, "Parsed created task: " + task.getTitle());
            return task;

        } catch (Exception e) {
            Log.e(TAG, "Lỗi parse JSON task", e);
            return null;
        }
    }
}