package com.example.myapp.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.android.material.textfield.TextInputEditText;
import com.example.myapp.R;
import com.example.myapp.models.Category;
import com.example.myapp.services.CategoryAPI;

/**
 * Dialog để thêm category mới
 * Hiển thị form nhập tên, mô tả và chọn màu
 */
public class AddCategoryDialog extends Dialog {

    private static final String TAG = "AddCategoryDialog";
    
    // Views
    private TextInputEditText editTextCategoryName;
    private TextInputEditText editTextCategoryDescription;
    private View selectedColorPreview;
    private TextView textSelectedColor;
    private Button buttonCancel, buttonSave;
    
    // Color selection views
    private View colorBlue, colorGreen, colorOrange, colorRed, colorPurple, colorTeal;
    
    // Data
    private String selectedColor = "#2196F3"; // Màu mặc định (xanh dương)
    private OnCategoryCreatedListener listener;
    
    /**
     * Interface để thông báo khi category được tạo thành công
     */
    public interface OnCategoryCreatedListener {
        void onCategoryCreated(Category category);
        void onError(String errorMessage);
    }

    public AddCategoryDialog(@NonNull Context context) {
        super(context);
    }

    public AddCategoryDialog(@NonNull Context context, OnCategoryCreatedListener listener) {
        super(context);
        this.listener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_add_category);
        
        // Khởi tạo views
        initViews();
        
        // Thiết lập click listeners
        setupClickListeners();
        
        // Thiết lập color picker
        setupColorPicker();
        
        Log.d(TAG, "AddCategoryDialog được khởi tạo");
    }

    /**
     * Khởi tạo tất cả views
     */
    private void initViews() {
        // Text inputs
        editTextCategoryName = findViewById(R.id.editTextCategoryName);
        editTextCategoryDescription = findViewById(R.id.editTextCategoryDescription);
        
        // Color preview
        selectedColorPreview = findViewById(R.id.selectedColorPreview);
        textSelectedColor = findViewById(R.id.textSelectedColor);
        
        // Buttons
        buttonCancel = findViewById(R.id.buttonCancel);
        buttonSave = findViewById(R.id.buttonSave);
        
        // Color selection views
        colorBlue = findViewById(R.id.colorBlue);
        colorGreen = findViewById(R.id.colorGreen);
        colorOrange = findViewById(R.id.colorOrange);
        colorRed = findViewById(R.id.colorRed);
        colorPurple = findViewById(R.id.colorPurple);
        colorTeal = findViewById(R.id.colorTeal);
        
        Log.d(TAG, "Đã khởi tạo tất cả views");
    }

    /**
     * Thiết lập click listeners cho buttons
     */
    private void setupClickListeners() {
        // Nút Hủy - đóng dialog
        buttonCancel.setOnClickListener(v -> {
            Log.d(TAG, "User click Hủy");
            dismiss();
        });
        
        // Nút Lưu - tạo category mới
        buttonSave.setOnClickListener(v -> {
            Log.d(TAG, "User click Lưu");
            createCategory();
        });
    }

    /**
     * Thiết lập color picker - cho phép user chọn màu
     */
    private void setupColorPicker() {
        // Thiết lập click listener cho từng màu
        setupColorClickListener(colorBlue, "#2196F3");
        setupColorClickListener(colorGreen, "#4CAF50");
        setupColorClickListener(colorOrange, "#FF9800");
        setupColorClickListener(colorRed, "#F44336");
        setupColorClickListener(colorPurple, "#9C27B0");
        setupColorClickListener(colorTeal, "#009688");
        
        // Set màu mặc định
        updateSelectedColor("#2196F3");
        
        Log.d(TAG, "Đã thiết lập color picker");
    }

    /**
     * Thiết lập click listener cho một màu cụ thể
     * @param colorView View màu
     * @param colorCode Mã màu hex
     */
    private void setupColorClickListener(View colorView, String colorCode) {
        colorView.setOnClickListener(v -> {
            Log.d(TAG, "User chọn màu: " + colorCode);
            updateSelectedColor(colorCode);
        });
    }

    /**
     * Cập nhật màu đã chọn
     * @param colorCode Mã màu hex
     */
    private void updateSelectedColor(String colorCode) {
        selectedColor = colorCode;
        
        // Cập nhật preview màu
        selectedColorPreview.setBackgroundColor(Color.parseColor(colorCode));
        textSelectedColor.setText(colorCode);
        
        Log.d(TAG, "Đã cập nhật màu đã chọn: " + colorCode);
    }

    /**
     * Tạo category mới
     * Validate input và gọi API
     */
    private void createCategory() {
        // Lấy dữ liệu từ form
        String name = editTextCategoryName.getText().toString().trim();
        String description = editTextCategoryDescription.getText().toString().trim();
        
        // Validate tên category (bắt buộc)
        if (TextUtils.isEmpty(name)) {
            editTextCategoryName.setError("Vui lòng nhập tên chủ đề");
            editTextCategoryName.requestFocus();
            return;
        }
        
        // Tạo Category object
        Category newCategory = new Category();
        newCategory.setName(name);
        newCategory.setDescription(description.isEmpty() ? "Không có mô tả" : description);
        newCategory.setColor(selectedColor);
        newCategory.setIcon("folder"); // Icon mặc định
        newCategory.setUserId("685ba33d4ed50aba7252bb4d"); // UserId fix cứng
        
        Log.d(TAG, "Bắt đầu tạo category: " + name + " với màu: " + selectedColor);
        
        // Disable nút Save để tránh click nhiều lần
        buttonSave.setEnabled(false);
        buttonSave.setText("Đang lưu...");

        // Timeout safety - enable lại button sau 10 giây nếu không có response
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!buttonSave.isEnabled()) {
                Log.w(TAG, "Timeout detected - callback might have failed");
                Log.w(TAG, "Data might be saved but callback not working");

                // Nếu dữ liệu đã được lưu (response 201) nhưng callback không hoạt động
                // Thì force close dialog
                forceCloseDialog();
            }
        }, 8000); // 8 giây

        // Gọi API tạo category
        CategoryAPI.createCategory(newCategory, new CategoryAPI.CreateCategoryCallback() {
            @Override
            public void onSuccess(Category createdCategory) {
                Log.d(TAG, "=== CALLBACK SUCCESS ===");
                Log.d(TAG, "createdCategory: " + (createdCategory != null ? createdCategory.toString() : "null"));
                if (createdCategory != null) {
                    Log.d(TAG, "Category ID: " + createdCategory.getId());
                    Log.d(TAG, "Category Name: " + createdCategory.getName());
                }
                
                // Chạy trên UI thread bằng Handler
                new Handler(Looper.getMainLooper()).post(() -> {
                    Toast.makeText(getContext(),
                        "Đã tạo chủ đề: " + createdCategory.getName(),
                        Toast.LENGTH_SHORT).show();

                    // Thông báo cho listener
                    if (listener != null) {
                        listener.onCategoryCreated(createdCategory);
                    }

                    // Đóng dialog
                    dismiss();
                });
            }
            
            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Lỗi tạo category: " + errorMessage);
                
                // Chạy trên UI thread bằng Handler
                new Handler(Looper.getMainLooper()).post(() -> {
                    Toast.makeText(getContext(),
                        "Lỗi tạo chủ đề: " + errorMessage,
                        Toast.LENGTH_LONG).show();

                    // Enable lại nút Save
                    buttonSave.setEnabled(true);
                    buttonSave.setText("Lưu");

                    // Thông báo cho listener
                    if (listener != null) {
                        listener.onError(errorMessage);
                    }
                });
            }
        });
    }

    /**
     * Set listener để nhận thông báo khi category được tạo
     * @param listener OnCategoryCreatedListener
     */
    public void setOnCategoryCreatedListener(OnCategoryCreatedListener listener) {
        this.listener = listener;
    }

    /**
     * Force close dialog và reset button (backup method)
     */
    private void forceCloseDialog() {
        Log.d(TAG, "Force closing dialog");

        // Reset button state
        buttonSave.setEnabled(true);
        buttonSave.setText("Lưu");

        // Close dialog
        dismiss();

        Toast.makeText(getContext(), "Đã lưu category thành công", Toast.LENGTH_SHORT).show();
    }
}
