const mongoose = require('mongoose');

const subTaskSchema = new mongoose.Schema({
  title: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 200
  },
  description: { 
    type: String,
    maxlength: 500
  },
  isCompleted: { 
    type: Boolean, 
    default: false 
  },
  completedAt: { 
    type: Date 
  },
  order: { 
    type: Number, 
    default: 0 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Index để tăng tốc query
subTaskSchema.index({ order: 1 });

// Middleware để update updatedAt
subTaskSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Tự động set completedAt khi subtask được hoàn thành
  if (this.isCompleted && !this.completedAt) {
    this.completedAt = Date.now();
  }
  
  // Xóa completedAt khi subtask chưa hoàn thành
  if (!this.isCompleted && this.completedAt) {
    this.completedAt = undefined;
  }
  
  next();
});

module.exports = mongoose.model('SubTask', subTaskSchema);
