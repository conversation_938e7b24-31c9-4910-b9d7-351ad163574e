const SubTask = require('../models/SubTask');
const Task = require('../models/Task');

// L<PERSON>y danh sách subtasks của một task
const getSubTasks = async (req, res) => {
  const { taskId } = req.params;
  const { userId } = req.query;

  try {
    // Kiểm tra task có tồn tại và thuộc về user không
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Lấy subtasks và sort theo order
    const subTasks = await SubTask.find({ _id: { $in: task.subTasks } })
      .sort({ order: 1 });

    res.json({ subTasks });
  } catch (error) {
    console.error('Get subtasks error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Tạo subtask mới
const createSubTask = async (req, res) => {
  const { taskId } = req.params;
  const { title, description, userId } = req.body;

  try {
    // Kiểm tra task có tồn tại và thuộc về user không
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Tính order cho subtask mới (cuối cùng)
    const maxOrder = task.subTasks.length;

    // Tạo subtask mới
    const subTask = new SubTask({
      title: title.trim(),
      description: description || '',
      order: maxOrder,
      isCompleted: false
    });

    await subTask.save();

    // Thêm subtask vào task
    task.subTasks.push(subTask._id);
    task.totalSubTasks = task.subTasks.length;
    
    // Không thay đổi completedSubTasks vì subtask mới chưa hoàn thành
    await task.save();

    res.status(201).json(subTask);
  } catch (error) {
    console.error('Create subtask error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Cập nhật subtask
const updateSubTask = async (req, res) => {
  const { taskId, subTaskId } = req.params;
  const { title, description, userId } = req.body;

  try {
    // Kiểm tra task có tồn tại và thuộc về user không
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Kiểm tra subtask có thuộc về task này không
    if (!task.subTasks.includes(subTaskId)) {
      return res.status(404).json({ message: 'SubTask not found in this task' });
    }

    // Cập nhật subtask
    const subTask = await SubTask.findById(subTaskId);
    if (!subTask) {
      return res.status(404).json({ message: 'SubTask not found' });
    }

    if (title) subTask.title = title.trim();
    if (description !== undefined) subTask.description = description;

    await subTask.save();

    res.json(subTask);
  } catch (error) {
    console.error('Update subtask error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Toggle trạng thái hoàn thành của subtask
const toggleSubTask = async (req, res) => {
  const { taskId, subTaskId } = req.params;
  const { userId } = req.query;

  try {
    console.log('=== Toggle SubTask Debug ===');
    console.log('Task ID:', taskId);
    console.log('SubTask ID:', subTaskId);
    console.log('User ID:', userId);

    // Kiểm tra task có tồn tại và thuộc về user không
    const task = await Task.findOne({ _id: taskId, userId: userId }).populate('categoryId', 'name');
    if (!task) {
      console.log('Task not found for user:', userId);
      return res.status(404).json({ message: 'Task not found' });
    }

    console.log('Task found:', task.title);
    console.log('Task category:', task.categoryId ? task.categoryId.name : 'No category');
    console.log('Task subTasks array:', task.subTasks);
    console.log('SubTasks count:', task.subTasks.length);

    // Kiểm tra subtask có thuộc về task này không
    // FIX: Convert subTaskId to ObjectId for proper comparison
    const mongoose = require('mongoose');
    let subTaskObjectId;

    try {
      subTaskObjectId = new mongoose.Types.ObjectId(subTaskId);
    } catch (error) {
      console.log('Invalid SubTask ID format:', subTaskId);
      return res.status(400).json({ message: 'Invalid SubTask ID format' });
    }

    const subTaskExists = task.subTasks.some(id => id.equals(subTaskObjectId));
    console.log('SubTask exists in task:', subTaskExists);

    if (!subTaskExists) {
      console.log('SubTask not found in task. Available SubTask IDs:', task.subTasks.map(id => id.toString()));
      return res.status(404).json({ message: 'SubTask not found in this task' });
    }

    // Toggle subtask
    const subTask = await SubTask.findById(subTaskId);
    if (!subTask) {
      console.log('SubTask document not found in database:', subTaskId);
      return res.status(404).json({ message: 'SubTask not found' });
    }

    console.log('SubTask before toggle:', { id: subTask._id, title: subTask.title, isCompleted: subTask.isCompleted });

    subTask.isCompleted = !subTask.isCompleted;
    await subTask.save();

    console.log('SubTask after toggle:', { id: subTask._id, title: subTask.title, isCompleted: subTask.isCompleted });

    // Cập nhật thống kê trong task
    await updateTaskCompletion(taskId);

    res.json(subTask);
  } catch (error) {
    console.error('Toggle subtask error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Xóa subtask
const deleteSubTask = async (req, res) => {
  const { taskId, subTaskId } = req.params;
  const { userId } = req.query;

  try {
    // Kiểm tra task có tồn tại và thuộc về user không
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Kiểm tra không thể xóa subtask cuối cùng
    if (task.subTasks.length <= 1) {
      return res.status(400).json({ message: 'Cannot delete the last subtask' });
    }

    // Kiểm tra subtask có thuộc về task này không
    if (!task.subTasks.includes(subTaskId)) {
      return res.status(404).json({ message: 'SubTask not found in this task' });
    }

    // Xóa subtask
    await SubTask.findByIdAndDelete(subTaskId);

    // Xóa subtask khỏi task
    task.subTasks = task.subTasks.filter(id => id.toString() !== subTaskId);
    task.totalSubTasks = task.subTasks.length;

    // Cập nhật lại order cho các subtasks còn lại
    const remainingSubTasks = await SubTask.find({ _id: { $in: task.subTasks } })
      .sort({ order: 1 });
    
    for (let i = 0; i < remainingSubTasks.length; i++) {
      remainingSubTasks[i].order = i;
      await remainingSubTasks[i].save();
    }

    await task.save();

    // Cập nhật thống kê trong task
    await updateTaskCompletion(taskId);

    res.json({ message: 'SubTask deleted successfully' });
  } catch (error) {
    console.error('Delete subtask error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Sắp xếp lại thứ tự subtasks
const reorderSubTasks = async (req, res) => {
  const { taskId } = req.params;
  const { subTaskIds, userId } = req.body; // Array of subtask IDs theo thứ tự mới

  try {
    // Kiểm tra task có tồn tại và thuộc về user không
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Kiểm tra tất cả subtaskIds có thuộc về task này không
    const taskSubTaskIds = task.subTasks.map(id => id.toString());
    const isValidOrder = subTaskIds.every(id => taskSubTaskIds.includes(id)) &&
                        subTaskIds.length === taskSubTaskIds.length;

    if (!isValidOrder) {
      return res.status(400).json({ message: 'Invalid subtask order' });
    }

    // Cập nhật order cho từng subtask
    for (let i = 0; i < subTaskIds.length; i++) {
      await SubTask.findByIdAndUpdate(subTaskIds[i], { order: i });
    }

    // Cập nhật thứ tự trong task.subTasks
    task.subTasks = subTaskIds;
    await task.save();

    res.json({ message: 'SubTasks reordered successfully' });
  } catch (error) {
    console.error('Reorder subtasks error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Helper function: Cập nhật completion status của task dựa trên subtasks
const updateTaskCompletion = async (taskId) => {
  try {
    const task = await Task.findById(taskId);
    if (!task) return;

    // Đếm số subtasks đã hoàn thành
    const completedSubTasks = await SubTask.countDocuments({
      _id: { $in: task.subTasks },
      isCompleted: true
    });

    task.completedSubTasks = completedSubTasks;

    // Tự động cập nhật trạng thái task
    const wasCompleted = task.isCompleted;
    task.isCompleted = (completedSubTasks === task.totalSubTasks && task.totalSubTasks > 0);

    // Log thay đổi trạng thái
    if (wasCompleted !== task.isCompleted) {
      console.log(`Task ${taskId} completion changed: ${wasCompleted} -> ${task.isCompleted}`);
    }

    await task.save();
  } catch (error) {
    console.error('Update task completion error:', error);
  }
};

module.exports = {
  getSubTasks,
  createSubTask,
  updateSubTask,
  toggleSubTask,
  deleteSubTask,
  reorderSubTasks,
  updateTaskCompletion
};
