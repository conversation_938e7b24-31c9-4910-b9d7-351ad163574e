package com.example.myapp.services;

import android.content.Context;
import android.util.Log;

import com.example.myapp.models.Task;
import com.example.myapp.models.Category;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ApiService {
    private static final String BASE_URL = "http://localhost:3000/api";
    private static final String TAG = "ApiService";
    
    private ExecutorService executor;
    private SimpleDateFormat dateFormat;

    public ApiService() {
        executor = Executors.newFixedThreadPool(4);
        dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
    }

    // Interfaces for callbacks
    public interface TasksCallback {
        void onSuccess(List<Task> tasks);
        void onError(String error);
    }

    public interface CategoriesCallback {
        void onSuccess(List<Category> categories);
        void onError(String error);
    }

    public interface TaskCallback {
        void onSuccess(Task task);
        void onError(String error);
    }

    public interface CategoryCallback {
        void onSuccess(Category category);
        void onError(String error);
    }

    public interface SimpleCallback {
        void onSuccess();
        void onError(String error);
    }

    // Get all tasks
    public void getTasks(TasksCallback callback) {
        executor.execute(() -> {
            try {
                URL url = new URL(BASE_URL + "/tasks");
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    List<Task> tasks = parseTasksFromJson(response.toString());
                    callback.onSuccess(tasks);
                } else {
                    callback.onError("HTTP Error: " + responseCode);
                }
                connection.disconnect();
            } catch (Exception e) {
                Log.e(TAG, "Error getting tasks", e);
                callback.onError(e.getMessage());
            }
        });
    }

    // Get all categories
    public void getCategories(CategoriesCallback callback) {
        executor.execute(() -> {
            try {
                URL url = new URL(BASE_URL + "/categories");
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    List<Category> categories = parseCategoriesFromJson(response.toString());
                    callback.onSuccess(categories);
                } else {
                    callback.onError("HTTP Error: " + responseCode);
                }
                connection.disconnect();
            } catch (Exception e) {
                Log.e(TAG, "Error getting categories", e);
                callback.onError(e.getMessage());
            }
        });
    }

    // Helper methods for JSON parsing
    private List<Task> parseTasksFromJson(String jsonString) throws JSONException {
        List<Task> tasks = new ArrayList<>();
        JSONArray jsonArray = new JSONArray(jsonString);
        
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject taskJson = jsonArray.getJSONObject(i);
            Task task = parseTaskFromJson(taskJson.toString());
            tasks.add(task);
        }
        
        return tasks;
    }

    private Task parseTaskFromJson(String jsonString) throws JSONException {
        JSONObject json = new JSONObject(jsonString);
        Task task = new Task();
        
        task.setId(json.optString("_id"));
        task.setTitle(json.optString("title"));
        task.setDescription(json.optString("description"));
        task.setCategoryId(json.optString("categoryId"));
        task.setPriority(json.optString("priority", "medium"));
        task.setCompleted(json.optBoolean("isCompleted", false));
        task.setImagePath(json.optString("imagePath"));
        task.setUserId(json.optString("userId"));
        
        // Parse dates
        try {
            if (json.has("startDate") && !json.isNull("startDate")) {
                task.setStartDate(dateFormat.parse(json.getString("startDate")));
            }
            if (json.has("deadline") && !json.isNull("deadline")) {
                task.setDeadline(dateFormat.parse(json.getString("deadline")));
            }
            if (json.has("completedAt") && !json.isNull("completedAt")) {
                task.setCompletedAt(dateFormat.parse(json.getString("completedAt")));
            }
            if (json.has("createdAt") && !json.isNull("createdAt")) {
                task.setCreatedAt(dateFormat.parse(json.getString("createdAt")));
            }
            if (json.has("updatedAt") && !json.isNull("updatedAt")) {
                task.setUpdatedAt(dateFormat.parse(json.getString("updatedAt")));
            }
        } catch (ParseException e) {
            Log.e(TAG, "Error parsing date", e);
        }
        
        return task;
    }

    private List<Category> parseCategoriesFromJson(String jsonString) throws JSONException {
        List<Category> categories = new ArrayList<>();
        JSONArray jsonArray = new JSONArray(jsonString);
        
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject categoryJson = jsonArray.getJSONObject(i);
            Category category = parseCategoryFromJson(categoryJson.toString());
            categories.add(category);
        }
        
        return categories;
    }

    private Category parseCategoryFromJson(String jsonString) throws JSONException {
        JSONObject json = new JSONObject(jsonString);
        Category category = new Category();
        
        category.setId(json.optString("_id"));
        category.setName(json.optString("name"));
        category.setDescription(json.optString("description"));
        category.setColor(json.optString("color", "#3498db"));
        category.setIcon(json.optString("icon", "folder"));
        category.setUserId(json.optString("userId"));
        
        // Parse dates
        try {
            if (json.has("createdAt") && !json.isNull("createdAt")) {
                category.setCreatedAt(dateFormat.parse(json.getString("createdAt")));
            }
            if (json.has("updatedAt") && !json.isNull("updatedAt")) {
                category.setUpdatedAt(dateFormat.parse(json.getString("updatedAt")));
            }
        } catch (ParseException e) {
            Log.e(TAG, "Error parsing date", e);
        }
        
        return category;
    }

    public void shutdown() {
        if (executor != null) {
            executor.shutdown();
        }
    }
}
