// Test script để debug SubTask API
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
const USER_ID = '68635f9b6f882821ae2a315c';

async function testSubTaskAPI() {
  try {
    console.log('=== Testing SubTask API ===');
    
    // 1. Get all tasks to see current data
    console.log('\n1. Getting all tasks...');
    const tasksResponse = await axios.get(`${BASE_URL}/tasks?userId=${USER_ID}`);
    const tasks = tasksResponse.data.tasks || tasksResponse.data;
    
    console.log(`Found ${tasks.length} tasks`);
    
    // Find a task with subtasks
    let taskWithSubTasks = null;
    for (const task of tasks) {
      if (task.subTasks && task.subTasks.length > 0) {
        taskWithSubTasks = task;
        break;
      }
    }
    
    if (!taskWithSubTasks) {
      console.log('No tasks with subtasks found!');
      return;
    }
    
    console.log('\n2. Found task with subtasks:');
    console.log('Task ID:', taskWithSubTasks._id);
    console.log('Task Title:', taskWithSubTasks.title);
    console.log('SubTasks:', taskWithSubTasks.subTasks.length);
    
    // Get detailed subtasks
    console.log('\n3. Getting detailed subtasks...');
    const subTasksResponse = await axios.get(`${BASE_URL}/tasks/${taskWithSubTasks._id}/subtasks?userId=${USER_ID}`);
    const subTasks = subTasksResponse.data;
    
    console.log('Detailed SubTasks:');
    subTasks.forEach((st, index) => {
      console.log(`  ${index + 1}. ID: ${st._id}, Title: ${st.title}, Completed: ${st.isCompleted}`);
    });
    
    if (subTasks.length === 0) {
      console.log('No detailed subtasks found!');
      return;
    }
    
    // Test toggle first subtask
    const firstSubTask = subTasks[0];
    console.log(`\n4. Testing toggle for SubTask: ${firstSubTask.title}`);
    console.log(`Current state: ${firstSubTask.isCompleted}`);
    
    const toggleUrl = `${BASE_URL}/tasks/${taskWithSubTasks._id}/subtasks/${firstSubTask._id}/toggle?userId=${USER_ID}`;
    console.log('Toggle URL:', toggleUrl);
    
    const toggleResponse = await axios.patch(toggleUrl);
    console.log('Toggle successful!');
    console.log('New state:', toggleResponse.data.isCompleted);
    
  } catch (error) {
    console.error('Error testing SubTask API:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Run test
testSubTaskAPI();
