<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#2196F3"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/buttonMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_sort_by_size"
            android:contentDescription="Menu"
            android:padding="12dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Lịch"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white" />

        <ImageButton
            android:id="@+id/buttonToday"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_today"

            android:contentDescription="Today"
            android:padding="12dp" />

    </LinearLayout>

    <!-- Calendar View -->
    <CalendarView
        android:id="@+id/calendarView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp" />

    <!-- Tasks for selected date -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/textSelectedDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Nhiệm vụ hôm nay"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="12dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewCalendarTasks"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/textNoTasksForDate"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:text="Không có nhiệm vụ nào trong ngày này"
            android:textSize="16sp"
            android:textColor="@android:color/darker_gray"
            android:gravity="center"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Footer Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:background="@android:color/white"
        android:elevation="8dp">

        <LinearLayout
            android:id="@+id/navTasks"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_agenda" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Nhiệm vụ"
                android:textSize="10sp"
                android:textColor="#666666"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/navCalendar"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_today" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lịch"
                android:textSize="10sp"
                android:textColor="#2196F3"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/navProfile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_myplaces" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cá nhân"
                android:textSize="10sp"
                android:textColor="#666666"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
