package com.example.myapp.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapp.R;
import com.example.myapp.models.Task;
import com.example.myapp.models.Category;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class SearchAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_TASK = 1;
    private static final int TYPE_CATEGORY = 2;

    private List<Object> searchResults;
    private OnSearchItemClickListener listener;
    private SimpleDateFormat dateFormat;

    public interface OnSearchItemClickListener {
        void onTaskClick(Task task);
        void onCategoryClick(Category category);
    }

    public SearchAdapter(List<Object> searchResults, OnSearchItemClickListener listener) {
        this.searchResults = searchResults;
        this.listener = listener;
        this.dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    }

    @Override
    public int getItemViewType(int position) {
        Object item = searchResults.get(position);
        if (item instanceof Task) {
            return TYPE_TASK;
        } else if (item instanceof Category) {
            return TYPE_CATEGORY;
        }
        return TYPE_TASK;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_TASK) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_search_task, parent, false);
            return new TaskViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_search_category, parent, false);
            return new CategoryViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        Object item = searchResults.get(position);

        if (holder instanceof TaskViewHolder && item instanceof Task) {
            ((TaskViewHolder) holder).bind((Task) item);
        } else if (holder instanceof CategoryViewHolder && item instanceof Category) {
            ((CategoryViewHolder) holder).bind((Category) item);
        }
    }

    @Override
    public int getItemCount() {
        return searchResults.size();
    }

    // Task ViewHolder
    public class TaskViewHolder extends RecyclerView.ViewHolder {
        private TextView textTaskTitle, textTaskDescription, textTaskDeadline, textTaskType;
        private View priorityIndicator;

        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            textTaskTitle = itemView.findViewById(R.id.textTaskTitle);
            textTaskDescription = itemView.findViewById(R.id.textTaskDescription);
            textTaskDeadline = itemView.findViewById(R.id.textTaskDeadline);
            textTaskType = itemView.findViewById(R.id.textTaskType);
            priorityIndicator = itemView.findViewById(R.id.priorityIndicator);
        }

        public void bind(Task task) {
            textTaskTitle.setText(task.getTitle());
            textTaskType.setText("Nhiệm vụ");

            if (task.getDescription() != null && !task.getDescription().trim().isEmpty()) {
                textTaskDescription.setText(task.getDescription());
                textTaskDescription.setVisibility(View.VISIBLE);
            } else {
                textTaskDescription.setVisibility(View.GONE);
            }

            if (task.getDeadline() != null) {
                textTaskDeadline.setText("Hạn: " + dateFormat.format(task.getDeadline()));
                textTaskDeadline.setVisibility(View.VISIBLE);
            } else {
                textTaskDeadline.setVisibility(View.GONE);
            }

            // Set priority color
            try {
                int priorityColor = Color.parseColor(task.getPriorityColor());
                priorityIndicator.setBackgroundColor(priorityColor);
            } catch (IllegalArgumentException e) {
                priorityIndicator.setBackgroundColor(Color.parseColor("#9E9E9E"));
            }

            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskClick(task);
                }
            });
        }
    }

    // Category ViewHolder
    public class CategoryViewHolder extends RecyclerView.ViewHolder {
        private TextView textCategoryName, textCategoryDescription, textCategoryType;
        private View categoryColorIndicator;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            textCategoryName = itemView.findViewById(R.id.textCategoryName);
            textCategoryDescription = itemView.findViewById(R.id.textCategoryDescription);
            textCategoryType = itemView.findViewById(R.id.textCategoryType);
            categoryColorIndicator = itemView.findViewById(R.id.categoryColorIndicator);
        }

        public void bind(Category category) {
            textCategoryName.setText(category.getName());
            textCategoryType.setText("Chủ đề");

            if (category.getDescription() != null && !category.getDescription().trim().isEmpty()) {
                textCategoryDescription.setText(category.getDescription());
                textCategoryDescription.setVisibility(View.VISIBLE);
            } else {
                textCategoryDescription.setVisibility(View.GONE);
            }

            // Set category color
            try {
                int color = Color.parseColor(category.getColor());
                categoryColorIndicator.setBackgroundColor(color);
            } catch (IllegalArgumentException e) {
                categoryColorIndicator.setBackgroundColor(Color.parseColor("#3498db"));
            }

            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCategoryClick(category);
                }
            });
        }
    }
}
