<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:orientation="vertical"
    android:elevation="8dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#2196F3"
        android:orientation="vertical"
        android:padding="24dp">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:src="@android:drawable/ic_menu_manage"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Todo App"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Quản lý công việc hiệu quả"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Menu Items -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="16dp">

            <!-- Tasks -->
            <LinearLayout
                android:id="@+id/nav_tasks"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="32dp"
                    android:text="Nhiệm vụ"
                    android:textSize="16sp"
                    android:textColor="#333333" />

            </LinearLayout>

            <!-- Calendar -->
            <LinearLayout
                android:id="@+id/nav_calendar"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_today"
                     />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="32dp"
                    android:text="Lịch"
                    android:textSize="16sp"
                    android:textColor="#333333" />

            </LinearLayout>

            <!-- Profile -->
            <LinearLayout
                android:id="@+id/nav_profile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_myplaces"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="32dp"
                    android:text="Cá nhân"
                    android:textSize="16sp"
                    android:textColor="#333333" />

            </LinearLayout>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:background="#E0E0E0" />

            <!-- Categories -->
            <LinearLayout
                android:id="@+id/nav_categories"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_sort_by_size"
                  />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="32dp"
                    android:text="Quản lý chủ đề"
                    android:textSize="16sp"
                    android:textColor="#333333" />

            </LinearLayout>

            <!-- Settings -->
            <LinearLayout
                android:id="@+id/nav_settings"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_preferences"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="32dp"
                    android:text="Cài đặt"
                    android:textSize="16sp"
                    android:textColor="#333333" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Footer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:text="Version 1.0.0"
        android:textSize="12sp"
        android:textColor="#999999"
        android:gravity="center" />

</LinearLayout>
