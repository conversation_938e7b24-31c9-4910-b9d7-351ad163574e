https://app.augmentcode.com/share/cfZ4P9qKPMU

1. ACTIVITIES - QUẢN LÝ MÀNG HÌNH
Bản chất: Activity là lifecycle manager - quản lý vòng đời của màn hình

onCreate()     // Khởi tạo màn hình lần đầu
onStart()      // Màn hình bắt đầu hiển thị
onResume()     // Màn hình sẵn sàng tương tác
onPause()      // Màn hình tạm dừng
onStop()       // Màn hình bị ẩn
onDestroy()    // Màn hình bị hủy

Chức năng:

Quản lý Fragment: Thêm, xóa, thay thế Fragment
Xử lý navigation: Chuyển đổi giữa các màn hình
Quản lý tài nguyên: Memory, CPU khi màn hình thay đổi
🧩 2. FRAGMENTS - THÀNH PHẦN GIAO DIỆN
Bản chất: Fragment là UI component - thành phần giao diện có thể tái sử dụng

Các hàm chính:


onCreate()           // Khởi tạo dữ liệu
onCreateView()       // Tạo giao diện (inflate layout)
onViewCreated()      // Thiết lập giao diện sau khi tạo
onActivityCreated()  // Activity đã sẵn sàng
onDestroyView()      // Hủy giao diện
onDestroy()          // Hủy fragment

Chức năng:

Hiển thị UI: Load layout, thiết lập views
Xử lý tương tác: Click, scroll, input
Quản lý dữ liệu: Load data, cập nhật UI
Giao tiếp: Với Activity, Service, Adapter
📦 3. MODELS - CẤU TRÚC DỮ LIỆU
Bản chất: Model là data structure - cấu trúc dữ liệu

Các thành phần:


// Properties (thuộc tính)
private String name;
private String color;

// Constructor (hàm khởi tạo)
public Category(String name, String color) { ... }

// Getters/Setters (hàm lấy/đặt giá trị)
public String getName() { return name; }
public void setName(String name) { this.name = name; }

// Business logic (logic nghiệp vụ)
public boolean isValidColor() { ... }
public String getPriorityColor() { ... }


Chức năng:

Lưu trữ dữ liệu: Thuộc tính của đối tượng
Validation: Kiểm tra tính hợp lệ
Business logic: Logic xử lý nghiệp vụ
Serialization: Chuyển đổi JSON ↔ Object
🔧 4. ADAPTERS - CẦU NỐI DỮ LIỆU VÀ GIAO DIỆN
Bản chất: Adapter là data-view bridge - cầu nối giữa dữ liệu và giao diện

Các hàm chính:


// Tạo ViewHolder (container cho 1 item)
onCreateViewHolder(ViewGroup parent, int viewType)

// Gắn dữ liệu vào ViewHolder
onBindViewHolder(ViewHolder holder, int position)

// Trả về số lượng items
getItemCount()

// Thông báo dữ liệu thay đổi
notifyDataSetChanged()
notifyItemInserted(position)
notifyItemRemoved(position)


Chức năng:

Mapping data → view: Chuyển dữ liệu thành giao diện
Recycling: Tái sử dụng view để tiết kiệm memory
Event handling: Xử lý click, long click
Dynamic updates: Cập nhật giao diện khi dữ liệu thay đổi
🌐 5. SERVICES - XỬ LÝ NGHIỆP VỤ
Bản chất: Service là business logic layer - tầng xử lý nghiệp vụ

Các hàm chính:

// Gọi API
public void getCategories(CategoriesCallback callback)
public void createTask(Task task, TaskCallback callback)

// Parse dữ liệu
private List<Category> parseJsonToCategories(String json)
private Category parseCategoryFromJson(String json)

// Xử lý lỗi
private void handleError(Exception e, ErrorCallback callback)


Chức năng:

API communication: Gọi REST API
Data transformation: JSON ↔ Object
Error handling: Xử lý lỗi network
Caching: Lưu cache dữ liệu
🔄 LUỒNG GỌI API VÀ XỬ LÝ DỮ LIỆU
📍 Nên gọi API ở đâu?
✅ GỌI API TRONG FRAGMENT:


// TodoFragment.java
private void loadCategories() {
    // Gọi API từ Fragment vì:
    // 1. Fragment quản lý UI
    // 2. Fragment biết khi nào cần load data
    // 3. Fragment xử lý kết quả và cập nhật UI
    
    categoryManager.loadCategoriesFromAPI(userId);
}


❌ KHÔNG gọi API trong:

Activity: Vì Activity chỉ quản lý Fragment
Adapter: Vì Adapter chỉ hiển thị dữ liệu có sẵn
Model: Vì Model chỉ chứa dữ liệu
💾 Lưu dữ liệu ở đâu?
1. Trong Fragment (tạm thời):

// TodoFragment.java
private List<Category> categories = new ArrayList<>();  // Dữ liệu UI
2. Trong Manager (quản lý):
// CategoryManager.java  
private List<Category> categories = new ArrayList<>();  // Dữ liệu business
3. Trong Database (lâu dài):


// SQLite, Room Database, SharedPreferences

 LUỒNG HOÀN CHỈNH GỌI API
Bước 1: Fragment yêu cầu dữ liệu

// TodoFragment.java
private void loadCategories() {
    // Fragment nói: "Tôi cần categories để hiển thị UI"
    categoryManager.loadCategoriesFromAPI(userId);
}

Bước 2: Manager gọi Service

// CategoryManager.java
public void loadCategoriesFromAPI(String userId) {
    // Manager nói: "Tôi sẽ gọi API để lấy dữ liệu"
    CategoryAPI.getCategories(userId, new CategoryAPI.CategoryCallback() {
        @Override
        public void onSuccess(List<Category> categoriesFromAPI) {
            // Lưu dữ liệu vào Manager
            categories.clear();
            categories.addAll(categoriesFromAPI);
            
            // Thông báo cho Fragment
            listener.onCategoriesLoaded(categories);
        }
    });
}

Bước 3: Service gọi API thực tế


// CategoryAPI.java
public static void getCategories(String userId, CategoryCallback callback) {
    // Service nói: "Tôi sẽ gọi HTTP request"
    new AsyncTask<String, Void, List<Category>>() {
        @Override
        protected List<Category> doInBackground(String... params) {
            // 1. Tạo HTTP request
            // 2. Gửi request lên server
            // 3. Nhận JSON response
            // 4. Parse JSON → List<Category>
            return categories;
        }
        
        @Override
        protected void onPostExecute(List<Category> categories) {
            // Trả kết quả về Manager
            callback.onSuccess(categories);
        }
    }.execute(userId);
}

Bước 4: Fragment nhận dữ liệu và cập nhật UI


// TodoFragment.java
@Override
public void onCategoriesLoaded(List<Category> categories) {
    // Fragment nói: "Tôi đã có dữ liệu, giờ cập nhật UI"
    
    // 1. Cập nhật danh sách local
    this.categories.clear();
    this.categories.addAll(categories);
    
    // 2. Thông báo Adapter cập nhật UI
    categoryAdapter.notifyDataSetChanged();
}

Bước 5: Adapter cập nhật giao diện

// CategoryAdapter.java
@Override
public void onBindViewHolder(CategoryViewHolder holder, int position) {
    // Adapter nói: "Tôi sẽ hiển thị từng category lên màn hình"
    Category category = categoryList.get(position);
    holder.textCategoryName.setText(category.getName());
    holder.categoryColorIndicator.setBackgroundColor(Color.parseColor(category.getColor()));
}

HÂN BIỆT VAI TRÒ
Fragment vs Adapter:
Fragment:

Quản lý dữ liệu: Load, lưu, xử lý dữ liệu
Điều phối: Gọi API, xử lý callback
Quản lý UI: Thiết lập RecyclerView, handle navigation
Business logic: Logic nghiệp vụ của màn hình
Adapter:

Hiển thị dữ liệu: Chuyển data thành view
Xử lý sự kiện UI: Click, long click trên item
Tối ưu performance: Recycling view
Presentation logic: Logic hiển thị

Thành phần	Vai trò chính	Ví dụ
Activity	Quản lý màn hình	Chuyển đổi Fragment
Fragment	Quản lý UI + Data	Gọi API, xử lý logic
Adapter	Hiển thị danh sách	Render categories lên RecyclerView
Model	Cấu trúc dữ liệu	Category, Task objects
Service	Xử lý nghiệp vụ	Gọi API, parse JSON
Kết luận: Fragment là "não bộ" quản lý, Adapter là "tay chân" hiển thị! 🧠👐



https://app.augmentcode.com/share/9Uo6j1WOU7c


1. Lấy tất cả categories
Method: GET
URL: http://localhost:5000/api/categories
Query Parameters: ?userId=685ba33d4ed50aba7252bb4d

2. Tạo category mới
Method: POST
URL: http://localhost:5000/api/categories
Headers: Content-Type: application/json

Body (raw JSON):
{
  "name": "Học tập",
  "description": "Các hoạt động học tập",
  "color": "#3498db",
  "icon": "book",
  "userId": "685ba33d4ed50aba7252bb4d"
}

3. Lấy category theo ID
Method: GET
URL: http://localhost:5000/api/categories/{categoryId}
Query Parameters: ?userId=685ba33d4ed50aba7252bb4d

Example: http://localhost:5000/api/categories/685ba33e4ed50aba7252bb58?userId=685ba33d4ed50aba7252bb4d

4. Cập nhật category
Method: PUT
URL: http://localhost:5000/api/categories/{categoryId}
Headers: Content-Type: application/json

Body (raw JSON):
{
  "name": "Học tập nâng cao",
  "description": "Các hoạt động học tập chuyên sâu",
  "color": "#2980b9",
  "icon": "graduation-cap",
  "userId": "685ba33d4ed50aba7252bb4d"
}

5. Xóa category

Method: DELETE
URL: http://localhost:5000/api/categories/{categoryId}

Example: http://localhost:5000/api/categories/685ba33e4ed50aba7252bb58

6. Thống kê category
Method: GET
URL: http://localhost:5000/api/categories/stats
Query Parameters: ?userId=685ba33d4ed50aba7252bb4d

7 . Lấy tất cả tasks
Method: GET
URL: http://localhost:5000/api/tasks
Query Parameters: 
- userId=685ba33d4ed50aba7252bb4d (bắt buộc)
- page=1 (tùy chọn)
- limit=10 (tùy chọn)
- categoryId=685ba33e4ed50aba7252bb58 (tùy chọn)
- search=keyword (tùy chọn)
- isCompleted=true/false (tùy chọn)
- priority=high/medium/low (tùy chọn)
- sortBy=deadline/createdAt/title (tùy chọn)
- order=asc/desc (tùy chọn)

Examples:
- Tất cả tasks: http://localhost:5000/api/tasks?userId=685ba33d4ed50aba7252bb4d
- Tasks theo category: http://localhost:5000/api/tasks?userId=685ba33d4ed50aba7252bb4d&categoryId=685ba33e4ed50aba7252bb58
- Tasks chưa hoàn thành: http://localhost:5000/api/tasks?userId=685ba33d4ed50aba7252bb4d&isCompleted=false
- Phân trang: http://localhost:5000/api/tasks?userId=685ba33d4ed50aba7252bb4d&page=1&limit=5

8 Tạo task mới
Method: POST
URL: http://localhost:5000/api/tasks
Headers: Content-Type: application/json

Body (raw JSON):
{
  "title": "Học Node.js",
  "description": "Hoàn thành khóa học Node.js",
  "categoryId": "685ba33e4ed50aba7252bb58",
  "startDate": "2025-01-01T10:00:00.000Z",
  "deadline": "2025-01-15T10:00:00.000Z",
  "priority": "medium",
  "userId": "685ba33d4ed50aba7252bb4d"
}

3. Lấy task theo ID
Method: GET
URL: http://localhost:5000/api/tasks/{taskId}
Query Parameters: ?userId=685ba33d4ed50aba7252bb4d

Example: http://localhost:5000/api/tasks/685ba33f4ed50aba7252bb9a?userId=685ba33d4ed50aba7252bb4d

4. Cập nhật task

Method: PUT
URL: http://localhost:5000/api/tasks/{taskId}
Headers: Content-Type: application/json

Body (raw JSON):
{
  "title": "Học Node.js nâng cao",
  "description": "Hoàn thành khóa học Node.js và Express",
  "categoryId": "685ba33e4ed50aba7252bb58",
  "startDate": "2025-01-01T10:00:00.000Z",
  "deadline": "2025-01-20T10:00:00.000Z",
  "priority": "high",
  "isCompleted": false
}

5. Toggle trạng thái task (hoàn thành/chưa hoàn thành)
Method: PATCH
URL: http://localhost:5000/api/tasks/{taskId}/toggle
Headers: Content-Type: application/json

Body (raw JSON):
{
  "userId": "685ba33d4ed50aba7252bb4d"
}

6. Xóa task
Method: DELETE
URL: http://localhost:5000/api/tasks/{taskId}

Example: http://localhost:5000/api/tasks/685ba33f4ed50aba7252bb9a

7. Thống kê tasks
Method: GET
URL: http://localhost:5000/api/tasks/stats
Query Parameters: ?userId=685ba33d4ed50aba7252bb4d

685ba33e4ed50aba7252bb58 (Giải trí)
685cb38d3fe5e68fd15a01fb (Phát triển bản thân)

685ba33f4ed50aba7252bb9a

685ba33d4ed50aba7252bb4d

GET Categories → Lấy danh sách categories
POST Category → Tạo category mới
GET Tasks → Lấy danh sách tasks
POST Task → Tạo task mới với categoryId từ bước 2
GET Tasks by Category → Filter tasks theo category
PATCH Toggle Task → Đánh dấu hoàn thành
PUT Update Task → Cập nhật task
DELETE Task → Xóa task
