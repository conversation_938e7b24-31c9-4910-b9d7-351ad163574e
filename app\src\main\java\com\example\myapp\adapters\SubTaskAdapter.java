package com.example.myapp.adapters;

import android.graphics.Paint;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapp.R;
import com.example.myapp.models.SubTask;

import java.util.List;

public class SubTaskAdapter extends RecyclerView.Adapter<SubTaskAdapter.SubTaskViewHolder> {

    private List<SubTask> subTaskList;
    private OnSubTaskClickListener listener;

    public interface OnSubTaskClickListener {
        void onSubTaskToggle(SubTask subTask, int position);
        void onSubTaskClick(SubTask subTask, int position);
    }

    public SubTaskAdapter(List<SubTask> subTaskList, OnSubTaskClickListener listener) {
        this.subTaskList = subTaskList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SubTaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_subtask, parent, false);
        return new SubTaskViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SubTaskViewHolder holder, int position) {
        SubTask subTask = subTaskList.get(position);
        holder.bind(subTask, position);
    }

    @Override
    public int getItemCount() {
        return subTaskList.size();
    }

    public void updateSubTasks(List<SubTask> newSubTasks) {
        this.subTaskList = newSubTasks;
        notifyDataSetChanged();
    }

    public void refreshSubTask(int position) {
        if (position >= 0 && position < subTaskList.size()) {
            notifyItemChanged(position);
        }
    }

    public class SubTaskViewHolder extends RecyclerView.ViewHolder {
        private CheckBox checkboxSubTask;
        private TextView textSubTaskTitle, textSubTaskDescription;

        public SubTaskViewHolder(@NonNull View itemView) {
            super(itemView);
            checkboxSubTask = itemView.findViewById(R.id.checkboxSubTask);
            textSubTaskTitle = itemView.findViewById(R.id.textSubTaskTitle);
            textSubTaskDescription = itemView.findViewById(R.id.textSubTaskDescription);
        }

        public void bind(SubTask subTask, int position) {
            // Set subtask title
            textSubTaskTitle.setText(subTask.getTitle());

            // Set subtask description
            if (subTask.getDescription() != null && !subTask.getDescription().trim().isEmpty()) {
                textSubTaskDescription.setText(subTask.getDescription());
                textSubTaskDescription.setVisibility(View.VISIBLE);
            } else {
                textSubTaskDescription.setVisibility(View.GONE);
            }

            // IMPORTANT: Clear listener trước khi set state để tránh trigger
            checkboxSubTask.setOnCheckedChangeListener(null);

            // Set completion status
            checkboxSubTask.setChecked(subTask.isCompleted());

            // Apply strikethrough effect if completed
            if (subTask.isCompleted()) {
                textSubTaskTitle.setPaintFlags(textSubTaskTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                textSubTaskTitle.setAlpha(0.6f);
                if (textSubTaskDescription.getVisibility() == View.VISIBLE) {
                    textSubTaskDescription.setPaintFlags(textSubTaskDescription.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                    textSubTaskDescription.setAlpha(0.6f);
                }
            } else {
                textSubTaskTitle.setPaintFlags(textSubTaskTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                textSubTaskTitle.setAlpha(1.0f);
                if (textSubTaskDescription.getVisibility() == View.VISIBLE) {
                    textSubTaskDescription.setPaintFlags(textSubTaskDescription.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                    textSubTaskDescription.setAlpha(1.0f);
                }
            }

            // Set click listeners SAU KHI đã set state
            checkboxSubTask.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (listener != null) {
                    // Log để debug
                    Log.d("SubTaskAdapter", "Checkbox clicked: " + subTask.getTitle() + " - isChecked: " + isChecked + " - subTask.isCompleted(): " + subTask.isCompleted());

                    // Gọi API với state hiện tại của subTask (TRƯỚC khi checkbox thay đổi)
                    listener.onSubTaskToggle(subTask, position);
                }
            });

            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onSubTaskClick(subTask, position);
                }
            });
        }
    }
}
