const express = require('express');
const {
  getSubTasks,
  createSubTask,
  updateSubTask,
  toggleSubTask,
  deleteSubTask,
  reorderSubTasks
} = require('../controllers/subTaskController');
const router = express.Router();

// SubTask routes - nested under tasks
// GET /api/tasks/:taskId/subtasks
router.get('/:taskId/subtasks', getSubTasks);

// POST /api/tasks/:taskId/subtasks
router.post('/:taskId/subtasks', createSubTask);

// PUT /api/tasks/:taskId/subtasks/:subTaskId
router.put('/:taskId/subtasks/:subTaskId', updateSubTask);

// PATCH /api/tasks/:taskId/subtasks/:subTaskId/toggle
router.patch('/:taskId/subtasks/:subTaskId/toggle', toggleSubTask);

// DELETE /api/tasks/:taskId/subtasks/:subTaskId
router.delete('/:taskId/subtasks/:subTaskId', deleteSubTask);

// POST /api/tasks/:taskId/subtasks/reorder
router.post('/:taskId/subtasks/reorder', reorderSubTasks);

module.exports = router;
