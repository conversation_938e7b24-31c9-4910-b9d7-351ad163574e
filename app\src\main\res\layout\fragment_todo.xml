<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="#2196F3"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:elevation="4dp">

            <ImageButton
                android:id="@+id/buttonMenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_sort_by_size"

                android:contentDescription="Menu"
                android:padding="12dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:text="Nhiệm vụ"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white" />

            <ImageButton
                android:id="@+id/buttonSearch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_search"

                android:contentDescription="Search"
                android:padding="12dp" />

        </LinearLayout>

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Categories Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Chủ đề"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/black" />

                    <Button
                        android:id="@+id/buttonAddCategory"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:text="+ Thêm"
                        android:textSize="12sp"
                        android:textColor="@android:color/white"
                        android:backgroundTint="#4CAF50"
                        android:minWidth="0dp"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewCategories"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal" />

            </LinearLayout>

            <!-- Tasks Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Danh sách nhiệm vụ"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:layout_marginBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewTasks"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <!-- Empty state -->
                <TextView
                    android:id="@+id/textEmptyState"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:text="Chưa có nhiệm vụ nào!\nHãy thêm nhiệm vụ đầu tiên."
                    android:textSize="16sp"
                    android:textColor="@android:color/darker_gray"
                    android:gravity="center"
                    android:visibility="gone" />

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/fabAddTask"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|end"
                    android:layout_margin="16dp"
                    android:contentDescription="Add task"
                    android:src="@android:drawable/ic_input_add"
                    app:backgroundTint="#4CAF50"
                    app:tint="@android:color/white" />

            </LinearLayout>

        </LinearLayout>

        <!-- Footer Navigation -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:orientation="horizontal"
            android:background="@android:color/white"
            android:elevation="8dp">

            <LinearLayout
                android:id="@+id/navCalendar"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_today" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="Lịch"
                    android:textColor="#666666"
                    android:textSize="10sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/navTasks"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="Nhiệm vụ"
                    android:textColor="#2196F3"
                    android:textSize="10sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/navProfile"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_myplaces" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="Cá nhân"
                    android:textColor="#666666"
                    android:textSize="10sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Floating Action Button -->

    </LinearLayout>

    <!-- Navigation Drawer -->
    <FrameLayout
        android:id="@+id/navContainer"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start" />

</androidx.drawerlayout.widget.DrawerLayout>
