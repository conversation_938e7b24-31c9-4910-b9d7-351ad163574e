package com.example.myapp.services;

import android.os.AsyncTask;
import android.util.Log;

import com.example.myapp.models.SubTask;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * Class chuyên xử lý API liên quan đến SubTasks
 */
public class SubTaskAPI {
    
    private static final String TAG = "SubTaskAPI";
    private static final String BASE_URL = "http://10.0.2.2:5000/api";
    
    /**
     * Interface để trả kết quả toggle subtask
     */
    public interface SubTaskCallback {
        void onSuccess(SubTask subTask);
        void onError(String errorMessage);
    }
    
    /**
     * Toggle completion status của subtask
     * @param taskId ID của task chứa subtask
     * @param subTaskId ID của subtask
     * @param userId ID của user
     * @param callback Interface để trả kết quả
     */
    public static void toggleSubTask(String taskId, String subTaskId, String userId, SubTaskCallback callback) {
        new ToggleSubTaskTask(taskId, subTaskId, userId, callback).execute();
    }
    
    /**
     * AsyncTask để toggle subtask completion
     */
    private static class ToggleSubTaskTask extends AsyncTask<Void, Void, String> {
        private String taskId;
        private String subTaskId;
        private String userId;
        private SubTaskCallback callback;
        private Exception exception;
        
        public ToggleSubTaskTask(String taskId, String subTaskId, String userId, SubTaskCallback callback) {
            this.taskId = taskId;
            this.subTaskId = subTaskId;
            this.userId = userId;
            this.callback = callback;
        }
        
        @Override
        protected String doInBackground(Void... voids) {
            try {
                String urlString = BASE_URL + "/tasks/" + taskId + "/subtasks/" + subTaskId + "/toggle?userId=" + userId;
                URL url = new URL(urlString);

                Log.d(TAG, "=== SubTaskAPI Toggle Debug ===");
                Log.d(TAG, "Task ID: " + taskId);
                Log.d(TAG, "SubTask ID: " + subTaskId);
                Log.d(TAG, "User ID: " + userId);
                Log.d(TAG, "Full URL: " + urlString);
                
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("PATCH");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response code: " + responseCode);
                
                BufferedReader reader;
                if (responseCode >= 200 && responseCode < 300) {
                    reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                } else {
                    reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                }
                
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                connection.disconnect();
                
                String responseString = response.toString();
                Log.d(TAG, "Response: " + responseString);
                
                if (responseCode >= 200 && responseCode < 300) {
                    return responseString;
                } else {
                    throw new Exception("HTTP Error: " + responseCode + " - " + responseString);
                }
                
            } catch (Exception e) {
                this.exception = e;
                Log.e(TAG, "Lỗi toggle subtask", e);
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(String result) {
            if (exception != null) {
                callback.onError("Lỗi kết nối: " + exception.getMessage());
                return;
            }
            
            if (result != null) {
                try {
                    // Parse response JSON to SubTask object
                    JSONObject jsonResponse = new JSONObject(result);
                    
                    SubTask subTask = new SubTask();
                    subTask.set_id(jsonResponse.optString("_id"));
                    subTask.setTitle(jsonResponse.optString("title"));
                    subTask.setDescription(jsonResponse.optString("description"));
                    subTask.setCompleted(jsonResponse.optBoolean("isCompleted", false));
                    subTask.setOrder(jsonResponse.optInt("order", 0));
                    
                    Log.d(TAG, "Toggle subtask thành công: " + subTask.getTitle() + " - " + subTask.isCompleted());
                    callback.onSuccess(subTask);
                    
                } catch (Exception e) {
                    Log.e(TAG, "Lỗi parse response toggle subtask", e);
                    callback.onError("Lỗi xử lý dữ liệu: " + e.getMessage());
                }
            } else {
                callback.onError("Không nhận được phản hồi từ server");
            }
        }
    }
}
